# iOS Mirror - 4K@60fps Ultra Low Latency

A high-performance iOS device mirroring application for Windows that supports 4K resolution at 60fps with ultra-low latency, similar to AnyMirror.

## Features

- **4K Resolution Support**: Mirror iOS devices at 3840x2160 resolution
- **60 FPS Target**: Smooth 60 frames per second mirroring
- **Ultra-Low Latency**: Optimized for minimal delay between device and display
- **Hardware Acceleration**: Uses DirectX 11 and Media Foundation for optimal performance
- **High-Quality Audio**: Low-latency audio processing with WASAPI
- **Automatic Discovery**: Finds iOS devices on the network automatically
- **Modern UI**: Clean, responsive interface built with Dear ImGui

## System Requirements

- Windows 10/11 (64-bit)
- DirectX 11 compatible graphics card
- Visual Studio 2019/2022 (for building)
- CMake 3.20 or later
- At least 8GB RAM (16GB recommended for 4K)
- Gigabit network connection (recommended for 4K streaming)

## Building

### Prerequisites

1. Install Visual Studio 2019/2022 with C++ development tools
2. Install CMake (https://cmake.org/download/)
3. Clone or download this repository

### Build Steps

1. Open Command Prompt or PowerShell in the project directory
2. Run the build script:
   ```
   build.bat
   ```
   
   Or manually:
   ```
   mkdir build
   cd build
   cmake .. -G "Visual Studio 17 2022" -A x64
   cmake --build . --config Release
   ```

3. The executable will be created at `build/bin/Release/iOSMirror.exe`

## Usage

### Setting up iOS Device

1. Ensure your iOS device and Windows PC are on the same network
2. On your iOS device, open Control Center
3. Tap "Screen Mirroring" 
4. Your Windows PC should appear as "iOS Mirror" in the list

### Using the Application

1. Launch `iOSMirror.exe`
2. The application will automatically discover iOS devices on your network
3. Select your device from the "iOS Devices" list
4. Click "Connect" to start mirroring
5. Use the settings panel to adjust video and audio options

### Keyboard Shortcuts

- `F11`: Toggle fullscreen mode
- `Esc`: Exit fullscreen mode

## Technical Architecture

### Core Components

- **AirPlay Receiver**: Implements AirPlay protocol for iOS device communication
- **Video Decoder**: Hardware-accelerated H.264/H.265 decoding using Media Foundation
- **Audio Processor**: Low-latency audio processing with WASAPI
- **Renderer**: DirectX 11-based video rendering with scaling options
- **Device Discovery**: Bonjour/mDNS service discovery for automatic device detection

### Performance Optimizations

- **Hardware Decoding**: Uses GPU acceleration for video decoding
- **Zero-Copy Rendering**: Direct GPU texture rendering without CPU copies
- **Threaded Processing**: Separate threads for network, decoding, and rendering
- **Buffer Management**: Optimized buffer pools to minimize allocations
- **Low-Latency Audio**: WASAPI exclusive mode for minimal audio delay

## Configuration

The application supports various configuration options:

### Video Settings
- Scaling modes: Stretch, Fit, Fill
- Fullscreen support
- Hardware acceleration toggle

### Audio Settings
- Volume control
- Low-latency mode
- Sample rate selection

## Troubleshooting

### Common Issues

1. **Device not found**
   - Ensure both devices are on the same network
   - Check firewall settings (allow port 7000)
   - Restart the application

2. **Connection fails**
   - Verify network connectivity
   - Try restarting both devices
   - Check for network interference

3. **Poor performance**
   - Ensure hardware acceleration is enabled
   - Close other applications using GPU/network
   - Use wired network connection for best performance

4. **Audio issues**
   - Check Windows audio settings
   - Try disabling/enabling low-latency mode
   - Verify audio device is working

### Performance Tips

- Use a wired network connection for best quality
- Close unnecessary applications
- Ensure your graphics drivers are up to date
- For 4K streaming, use a Gigabit network connection

## Development

### Project Structure

```
├── src/                    # Source files
│   ├── main.cpp           # Application entry point
│   ├── Application.cpp    # Main application class
│   ├── AirPlayReceiver.cpp # AirPlay protocol implementation
│   ├── VideoDecoder.cpp   # Video decoding
│   ├── AudioProcessor.cpp # Audio processing
│   ├── Renderer.cpp       # Video rendering
│   ├── DeviceDiscovery.cpp # Device discovery
│   └── UI/                # User interface
├── include/               # Header files
├── third_party/          # External dependencies
├── CMakeLists.txt        # Build configuration
└── README.md            # This file
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is provided as-is for educational and development purposes.

## Acknowledgments

- Dear ImGui for the user interface
- Microsoft Media Foundation for video decoding
- DirectX 11 for hardware-accelerated rendering
- WASAPI for low-latency audio
