#pragma once

#include "Common.h"

class DeviceDiscovery {
public:
    DeviceDiscovery();
    ~DeviceDiscovery();

    bool Initialize();
    void Shutdown();
    void Update();

    // Discovery control
    bool StartDiscovery();
    void StopDiscovery();
    bool IsDiscovering() const { return m_isDiscovering; }

    // Device management
    std::vector<DeviceInfo> GetDiscoveredDevices() const;
    DeviceInfo* FindDevice(const std::string& deviceId);
    void RemoveDevice(const std::string& deviceId);

    // Callbacks
    std::function<void(const DeviceInfo&)> OnDeviceFound;
    std::function<void(const std::string&)> OnDeviceLost;

private:
    // Bonjour/mDNS
    bool InitializeBonjour();
    void CleanupBonjour();
    void BrowseServices();
    
    // Device tracking
    void AddOrUpdateDevice(const DeviceInfo& device);
    void CheckDeviceTimeouts();
    
    std::vector<DeviceInfo> m_devices;
    std::mutex m_devicesMutex;
    bool m_isDiscovering;
    
    // Threading
    std::thread m_discoveryThread;
    std::atomic<bool> m_shouldStop;
    
    // Bonjour handles (platform specific)
    void* m_browseRef; // DNSServiceRef on real implementation
};

class NetworkManager {
public:
    NetworkManager();
    ~NetworkManager();

    bool Initialize();
    void Shutdown();
    void Update();

    // Network info
    std::vector<std::string> GetLocalIPAddresses();
    bool IsNetworkAvailable();
    
private:
    bool m_initialized;
};
