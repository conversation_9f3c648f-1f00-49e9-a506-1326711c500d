#pragma once

#include "../Common.h"

class Timer {
public:
    Timer();
    ~Timer() = default;

    void Start();
    void Stop();
    void Reset();
    
    double GetElapsedSeconds() const;
    double GetElapsedMilliseconds() const;
    double GetElapsedMicroseconds() const;
    
    bool IsRunning() const { return m_isRunning; }
    
    // Static utility functions
    static uint64_t GetCurrentTimeUs();
    static uint64_t GetCurrentTimeMs();
    static void SleepUs(uint64_t microseconds);
    static void SleepMs(uint64_t milliseconds);

private:
    std::chrono::high_resolution_clock::time_point m_startTime;
    std::chrono::high_resolution_clock::time_point m_endTime;
    bool m_isRunning;
};
