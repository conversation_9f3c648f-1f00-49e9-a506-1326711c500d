#include "Utils/Logger.h"
#include <cstdarg>
#include <iomanip>
#include <sstream>

std::mutex Logger::s_mutex;
std::ofstream Logger::s_file;
LogLevel Logger::s_level = LogLevel::Info;
bool Logger::s_consoleOutput = true;
bool Logger::s_initialized = false;

void Logger::Initialize(const std::string& filename) {
    std::lock_guard<std::mutex> lock(s_mutex);
    
    if (s_initialized) {
        return;
    }
    
    s_file.open(filename, std::ios::out | std::ios::app);
    if (!s_file.is_open()) {
        std::cerr << "Failed to open log file: " << filename << std::endl;
        return;
    }
    
    s_initialized = true;
    Info("Logger initialized - iOS Mirror v1.0.0");
}

void Logger::Shutdown() {
    std::lock_guard<std::mutex> lock(s_mutex);
    
    if (s_initialized && s_file.is_open()) {
        Info("Logger shutting down");
        s_file.close();
    }
    
    s_initialized = false;
}

void Logger::SetLevel(LogLevel level) {
    std::lock_guard<std::mutex> lock(s_mutex);
    s_level = level;
}

void Logger::SetConsoleOutput(bool enabled) {
    std::lock_guard<std::mutex> lock(s_mutex);
    s_consoleOutput = enabled;
}

void Logger::Debug(const char* format, ...) {
    va_list args;
    va_start(args, format);
    Log(LogLevel::Debug, format, args);
    va_end(args);
}

void Logger::Info(const char* format, ...) {
    va_list args;
    va_start(args, format);
    Log(LogLevel::Info, format, args);
    va_end(args);
}

void Logger::Warning(const char* format, ...) {
    va_list args;
    va_start(args, format);
    Log(LogLevel::Warning, format, args);
    va_end(args);
}

void Logger::Error(const char* format, ...) {
    va_list args;
    va_start(args, format);
    Log(LogLevel::Error, format, args);
    va_end(args);
}

void Logger::Log(LogLevel level, const char* format, va_list args) {
    if (level < s_level) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(s_mutex);
    
    // Format the message
    char buffer[4096];
    vsnprintf(buffer, sizeof(buffer), format, args);
    
    // Create log entry
    std::string timestamp = GetTimestamp();
    std::string levelStr = LevelToString(level);
    std::string logEntry = "[" + timestamp + "] [" + levelStr + "] " + buffer;
    
    // Output to console
    if (s_consoleOutput) {
        if (level >= LogLevel::Error) {
            std::cerr << logEntry << std::endl;
        } else {
            std::cout << logEntry << std::endl;
        }
    }
    
    // Output to file
    if (s_initialized && s_file.is_open()) {
        s_file << logEntry << std::endl;
        s_file.flush();
    }
}

std::string Logger::GetTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    
    return ss.str();
}

std::string Logger::LevelToString(LogLevel level) {
    switch (level) {
        case LogLevel::Debug:   return "DEBUG";
        case LogLevel::Info:    return "INFO ";
        case LogLevel::Warning: return "WARN ";
        case LogLevel::Error:   return "ERROR";
        default:                return "UNKN ";
    }
}
