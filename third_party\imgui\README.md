# Dear ImGui Integration

This directory should contain the Dear ImGui library files.

## Setup Instructions

1. Download Dear I<PERSON><PERSON><PERSON> from: https://github.com/ocornut/imgui
2. Extract the following files to this directory:
   - imgui.cpp
   - imgui.h
   - imgui_demo.cpp
   - imgui_draw.cpp
   - imgui_tables.cpp
   - imgui_widgets.cpp
   - imconfig.h
   - imgui_internal.h
   - imstb_rectpack.h
   - imstb_textedit.h
   - imstb_truetype.h

3. From the backends folder, copy:
   - imgui_impl_win32.cpp
   - imgui_impl_win32.h
   - imgui_impl_dx11.cpp
   - imgui_impl_dx11.h

## Version

This project is designed to work with Dear ImGui v1.89 or later.
