#include "Application.h"
#include "AirPlayReceiver.h"
#include "VideoDecoder.h"
#include "AudioProcessor.h"
#include "Renderer.h"
#include "DeviceDiscovery.h"
#include "NetworkManager.h"
#include "UI/MainWindow.h"
#include "Utils/Logger.h"
#include "Utils/Timer.h"

Application::Application()
    : m_hwnd(nullptr)
    , m_windowWidth(1280)
    , m_windowHeight(720)
    , m_isRunning(false)
    , m_d3dDevice(nullptr)
    , m_d3dContext(nullptr)
    , m_swapChain(nullptr)
    , m_renderTargetView(nullptr)
    , m_depthStencilView(nullptr)
    , m_depthStencilBuffer(nullptr)
    , m_shouldExit(false)
    , m_deltaTime(0.0)
    , m_fps(0.0)
    , m_frameCount(0)
{
    ZeroMemory(&m_wc, sizeof(m_wc));
}

Application::~Application() {
    Shutdown();
}

bool Application::Initialize() {
    LOG_INFO("Initializing iOS Mirror Application...");

    // Initialize logger
    Logger::Initialize();
    Logger::SetLevel(LogLevel::Debug);

    // Initialize COM
    HRESULT hr = CoInitializeEx(nullptr, COINIT_MULTITHREADED);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to initialize COM: 0x%08X", hr);
        return false;
    }

    // Initialize Media Foundation
    hr = MFStartup(MF_VERSION);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to initialize Media Foundation: 0x%08X", hr);
        return false;
    }

    // Initialize Winsock
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        LOG_ERROR("Failed to initialize Winsock: %d", result);
        return false;
    }

    // Create window
    if (!CreateWindow()) {
        LOG_ERROR("Failed to create window");
        return false;
    }

    // Initialize DirectX
    if (!InitializeD3D()) {
        LOG_ERROR("Failed to initialize DirectX");
        return false;
    }

    // Initialize ImGui
    if (!InitializeImGui()) {
        LOG_ERROR("Failed to initialize ImGui");
        return false;
    }

    // Initialize components
    m_frameTimer = std::make_unique<Timer>();
    m_networkManager = std::make_unique<NetworkManager>();
    m_deviceDiscovery = std::make_unique<DeviceDiscovery>();
    m_airplayReceiver = std::make_unique<AirPlayReceiver>();
    m_videoDecoder = std::make_unique<VideoDecoder>(m_d3dDevice, m_d3dContext);
    m_audioProcessor = std::make_unique<AudioProcessor>();
    m_renderer = std::make_unique<Renderer>(m_d3dDevice, m_d3dContext);
    m_mainWindow = std::make_unique<MainWindow>();

    // Initialize components
    if (!m_networkManager->Initialize()) {
        LOG_ERROR("Failed to initialize network manager");
        return false;
    }

    if (!m_deviceDiscovery->Initialize()) {
        LOG_ERROR("Failed to initialize device discovery");
        return false;
    }

    if (!m_airplayReceiver->Initialize()) {
        LOG_ERROR("Failed to initialize AirPlay receiver");
        return false;
    }

    if (!m_videoDecoder->Initialize()) {
        LOG_ERROR("Failed to initialize video decoder");
        return false;
    }

    if (!m_audioProcessor->Initialize()) {
        LOG_ERROR("Failed to initialize audio processor");
        return false;
    }

    if (!m_renderer->Initialize()) {
        LOG_ERROR("Failed to initialize renderer");
        return false;
    }

    if (!m_mainWindow->Initialize()) {
        LOG_ERROR("Failed to initialize main window");
        return false;
    }

    m_isRunning = true;
    LOG_INFO("Application initialized successfully");
    return true;
}

void Application::Run() {
    LOG_INFO("Starting application main loop");
    
    m_frameTimer->Start();
    ShowWindow(m_hwnd, SW_SHOW);
    UpdateWindow(m_hwnd);

    MSG msg = {};
    while (m_isRunning && !m_shouldExit) {
        // Handle Windows messages
        while (PeekMessage(&msg, nullptr, 0, 0, PM_REMOVE)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
            
            if (msg.message == WM_QUIT) {
                m_isRunning = false;
                break;
            }
        }

        if (!m_isRunning) break;

        // Update and render
        Update();
        Render();

        // Calculate FPS
        m_frameCount++;
        if (m_frameCount % 60 == 0) {
            double elapsed = m_frameTimer->GetElapsedSeconds();
            m_fps = 60.0 / elapsed;
            m_frameTimer->Reset();
            m_frameTimer->Start();
        }
    }

    LOG_INFO("Application main loop ended");
}

void Application::Shutdown() {
    LOG_INFO("Shutting down application...");
    
    m_isRunning = false;
    m_shouldExit = true;

    // Wait for update thread to finish
    if (m_updateThread.joinable()) {
        m_updateThread.join();
    }

    // Shutdown components
    if (m_mainWindow) m_mainWindow->Shutdown();
    if (m_renderer) m_renderer->Shutdown();
    if (m_audioProcessor) m_audioProcessor->Shutdown();
    if (m_videoDecoder) m_videoDecoder->Shutdown();
    if (m_airplayReceiver) m_airplayReceiver->Shutdown();
    if (m_deviceDiscovery) m_deviceDiscovery->Shutdown();
    if (m_networkManager) m_networkManager->Shutdown();

    // Cleanup DirectX and ImGui
    CleanupImGui();
    CleanupD3D();

    // Cleanup window
    if (m_hwnd) {
        DestroyWindow(m_hwnd);
        m_hwnd = nullptr;
    }

    if (m_wc.lpszClassName) {
        UnregisterClass(m_wc.lpszClassName, m_wc.hInstance);
    }

    // Cleanup system resources
    WSACleanup();
    MFShutdown();
    CoUninitialize();

    Logger::Shutdown();
}

bool Application::CreateWindow() {
    HINSTANCE hInstance = GetModuleHandle(nullptr);
    
    m_wc.cbSize = sizeof(WNDCLASSEX);
    m_wc.style = CS_HREDRAW | CS_VREDRAW;
    m_wc.lpfnWndProc = WindowProc;
    m_wc.cbClsExtra = 0;
    m_wc.cbWndExtra = 0;
    m_wc.hInstance = hInstance;
    m_wc.hIcon = LoadIcon(nullptr, IDI_APPLICATION);
    m_wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    m_wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    m_wc.lpszMenuName = nullptr;
    m_wc.lpszClassName = L"iOSMirrorWindow";
    m_wc.hIconSm = LoadIcon(nullptr, IDI_APPLICATION);

    if (!RegisterClassEx(&m_wc)) {
        LOG_ERROR("Failed to register window class");
        return false;
    }

    RECT rect = { 0, 0, m_windowWidth, m_windowHeight };
    AdjustWindowRect(&rect, WS_OVERLAPPEDWINDOW, FALSE);

    m_hwnd = CreateWindowEx(
        0,
        m_wc.lpszClassName,
        L"iOS Mirror - 4K@60fps Ultra Low Latency",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        rect.right - rect.left,
        rect.bottom - rect.top,
        nullptr,
        nullptr,
        hInstance,
        this
    );

    if (!m_hwnd) {
        LOG_ERROR("Failed to create window");
        return false;
    }

    return true;
}

bool Application::InitializeD3D() {
    HRESULT hr;

    // Create device and swap chain
    DXGI_SWAP_CHAIN_DESC swapChainDesc = {};
    swapChainDesc.BufferCount = 2;
    swapChainDesc.BufferDesc.Width = m_windowWidth;
    swapChainDesc.BufferDesc.Height = m_windowHeight;
    swapChainDesc.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    swapChainDesc.BufferDesc.RefreshRate.Numerator = 60;
    swapChainDesc.BufferDesc.RefreshRate.Denominator = 1;
    swapChainDesc.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    swapChainDesc.OutputWindow = m_hwnd;
    swapChainDesc.SampleDesc.Count = 1;
    swapChainDesc.SampleDesc.Quality = 0;
    swapChainDesc.Windowed = TRUE;
    swapChainDesc.SwapEffect = DXGI_SWAP_EFFECT_FLIP_DISCARD;

    D3D_FEATURE_LEVEL featureLevels[] = {
        D3D_FEATURE_LEVEL_11_1,
        D3D_FEATURE_LEVEL_11_0,
        D3D_FEATURE_LEVEL_10_1,
        D3D_FEATURE_LEVEL_10_0
    };

    D3D_FEATURE_LEVEL featureLevel;
    hr = D3D11CreateDeviceAndSwapChain(
        nullptr,
        D3D_DRIVER_TYPE_HARDWARE,
        nullptr,
        D3D11_CREATE_DEVICE_DEBUG,
        featureLevels,
        ARRAYSIZE(featureLevels),
        D3D11_SDK_VERSION,
        &swapChainDesc,
        &m_swapChain,
        &m_d3dDevice,
        &featureLevel,
        &m_d3dContext
    );

    if (FAILED(hr)) {
        LOG_ERROR("Failed to create D3D11 device and swap chain: 0x%08X", hr);
        return false;
    }

    // Create render target view
    ID3D11Texture2D* backBuffer = nullptr;
    hr = m_swapChain->GetBuffer(0, __uuidof(ID3D11Texture2D), (void**)&backBuffer);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to get back buffer: 0x%08X", hr);
        return false;
    }

    hr = m_d3dDevice->CreateRenderTargetView(backBuffer, nullptr, &m_renderTargetView);
    backBuffer->Release();
    if (FAILED(hr)) {
        LOG_ERROR("Failed to create render target view: 0x%08X", hr);
        return false;
    }

    // Create depth stencil buffer
    D3D11_TEXTURE2D_DESC depthDesc = {};
    depthDesc.Width = m_windowWidth;
    depthDesc.Height = m_windowHeight;
    depthDesc.MipLevels = 1;
    depthDesc.ArraySize = 1;
    depthDesc.Format = DXGI_FORMAT_D24_UNORM_S8_UINT;
    depthDesc.SampleDesc.Count = 1;
    depthDesc.SampleDesc.Quality = 0;
    depthDesc.Usage = D3D11_USAGE_DEFAULT;
    depthDesc.BindFlags = D3D11_BIND_DEPTH_STENCIL;

    hr = m_d3dDevice->CreateTexture2D(&depthDesc, nullptr, &m_depthStencilBuffer);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to create depth stencil buffer: 0x%08X", hr);
        return false;
    }

    hr = m_d3dDevice->CreateDepthStencilView(m_depthStencilBuffer, nullptr, &m_depthStencilView);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to create depth stencil view: 0x%08X", hr);
        return false;
    }

    // Set render targets
    m_d3dContext->OMSetRenderTargets(1, &m_renderTargetView, m_depthStencilView);

    // Set viewport
    D3D11_VIEWPORT viewport = {};
    viewport.Width = (float)m_windowWidth;
    viewport.Height = (float)m_windowHeight;
    viewport.MinDepth = 0.0f;
    viewport.MaxDepth = 1.0f;
    viewport.TopLeftX = 0;
    viewport.TopLeftY = 0;
    m_d3dContext->RSSetViewports(1, &viewport);

    LOG_INFO("DirectX 11 initialized successfully");
    return true;
}

bool Application::InitializeImGui() {
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO();
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
    io.ConfigFlags |= ImGuiConfigFlags_DockingEnable;
    io.ConfigFlags |= ImGuiConfigFlags_ViewportsEnable;

    ImGui::StyleColorsDark();

    ImGuiStyle& style = ImGui::GetStyle();
    if (io.ConfigFlags & ImGuiConfigFlags_ViewportsEnable) {
        style.WindowRounding = 0.0f;
        style.Colors[ImGuiCol_WindowBg].w = 1.0f;
    }

    if (!ImGui_ImplWin32_Init(m_hwnd)) {
        LOG_ERROR("Failed to initialize ImGui Win32");
        return false;
    }

    if (!ImGui_ImplDX11_Init(m_d3dDevice, m_d3dContext)) {
        LOG_ERROR("Failed to initialize ImGui DX11");
        return false;
    }

    LOG_INFO("ImGui initialized successfully");
    return true;
}

void Application::CleanupD3D() {
    SAFE_RELEASE(m_depthStencilView);
    SAFE_RELEASE(m_depthStencilBuffer);
    SAFE_RELEASE(m_renderTargetView);
    SAFE_RELEASE(m_swapChain);
    SAFE_RELEASE(m_d3dContext);
    SAFE_RELEASE(m_d3dDevice);
}

void Application::CleanupImGui() {
    ImGui_ImplDX11_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();
}

void Application::Update() {
    // Update components
    if (m_deviceDiscovery) {
        m_deviceDiscovery->Update();
    }

    if (m_airplayReceiver) {
        m_airplayReceiver->Update();
    }

    if (m_videoDecoder) {
        m_videoDecoder->Update();
    }

    if (m_audioProcessor) {
        m_audioProcessor->Update();
    }
}

void Application::Render() {
    // Clear render target
    float clearColor[4] = { 0.0f, 0.0f, 0.0f, 1.0f };
    m_d3dContext->ClearRenderTargetView(m_renderTargetView, clearColor);
    m_d3dContext->ClearDepthStencilView(m_depthStencilView, D3D11_CLEAR_DEPTH | D3D11_CLEAR_STENCIL, 1.0f, 0);

    // Start ImGui frame
    ImGui_ImplDX11_NewFrame();
    ImGui_ImplWin32_NewFrame();
    ImGui::NewFrame();

    // Render main window UI
    if (m_mainWindow) {
        m_mainWindow->Render();
    }

    // Render video content
    if (m_renderer) {
        m_renderer->Render();
    }

    // Render ImGui
    ImGui::Render();
    ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());

    // Handle multi-viewport
    ImGuiIO& io = ImGui::GetIO();
    if (io.ConfigFlags & ImGuiConfigFlags_ViewportsEnable) {
        ImGui::UpdatePlatformWindows();
        ImGui::RenderPlatformWindowsDefault();
    }

    // Present
    m_swapChain->Present(1, 0);
}

LRESULT CALLBACK Application::WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    if (ImGui_ImplWin32_WndProcHandler(hwnd, uMsg, wParam, lParam))
        return true;

    Application* app = nullptr;
    if (uMsg == WM_NCCREATE) {
        CREATESTRUCT* pCreate = reinterpret_cast<CREATESTRUCT*>(lParam);
        app = reinterpret_cast<Application*>(pCreate->lpCreateParams);
        SetWindowLongPtr(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(app));
    } else {
        app = reinterpret_cast<Application*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
    }

    if (app) {
        switch (uMsg) {
            case WM_SIZE:
                if (wParam != SIZE_MINIMIZED) {
                    int width = LOWORD(lParam);
                    int height = HIWORD(lParam);
                    app->OnResize(width, height);
                }
                return 0;

            case WM_CLOSE:
                app->OnClose();
                return 0;

            case WM_DESTROY:
                PostQuitMessage(0);
                return 0;
        }
    }

    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

void Application::OnResize(int width, int height) {
    if (width <= 0 || height <= 0) return;

    m_windowWidth = width;
    m_windowHeight = height;

    if (m_d3dContext && m_swapChain) {
        // Release render target view
        SAFE_RELEASE(m_renderTargetView);
        SAFE_RELEASE(m_depthStencilView);
        SAFE_RELEASE(m_depthStencilBuffer);

        // Resize swap chain
        HRESULT hr = m_swapChain->ResizeBuffers(0, width, height, DXGI_FORMAT_UNKNOWN, 0);
        if (FAILED(hr)) {
            LOG_ERROR("Failed to resize swap chain: 0x%08X", hr);
            return;
        }

        // Recreate render target view
        ID3D11Texture2D* backBuffer = nullptr;
        hr = m_swapChain->GetBuffer(0, __uuidof(ID3D11Texture2D), (void**)&backBuffer);
        if (SUCCEEDED(hr)) {
            hr = m_d3dDevice->CreateRenderTargetView(backBuffer, nullptr, &m_renderTargetView);
            backBuffer->Release();
        }

        // Recreate depth stencil buffer
        D3D11_TEXTURE2D_DESC depthDesc = {};
        depthDesc.Width = width;
        depthDesc.Height = height;
        depthDesc.MipLevels = 1;
        depthDesc.ArraySize = 1;
        depthDesc.Format = DXGI_FORMAT_D24_UNORM_S8_UINT;
        depthDesc.SampleDesc.Count = 1;
        depthDesc.SampleDesc.Quality = 0;
        depthDesc.Usage = D3D11_USAGE_DEFAULT;
        depthDesc.BindFlags = D3D11_BIND_DEPTH_STENCIL;

        hr = m_d3dDevice->CreateTexture2D(&depthDesc, nullptr, &m_depthStencilBuffer);
        if (SUCCEEDED(hr)) {
            hr = m_d3dDevice->CreateDepthStencilView(m_depthStencilBuffer, nullptr, &m_depthStencilView);
        }

        // Set render targets
        m_d3dContext->OMSetRenderTargets(1, &m_renderTargetView, m_depthStencilView);

        // Update viewport
        D3D11_VIEWPORT viewport = {};
        viewport.Width = (float)width;
        viewport.Height = (float)height;
        viewport.MinDepth = 0.0f;
        viewport.MaxDepth = 1.0f;
        viewport.TopLeftX = 0;
        viewport.TopLeftY = 0;
        m_d3dContext->RSSetViewports(1, &viewport);
    }
}

void Application::OnClose() {
    m_isRunning = false;
}
