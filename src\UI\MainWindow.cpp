#include "UI/MainWindow.h"
#include "Utils/Logger.h"

MainWindow::MainWindow()
    : m_showDeviceList(true)
    , m_showSettings(false)
    , m_showStats(false)
    , m_showAbout(false)
    , m_selectedDevice(-1)
    , m_fullscreen(false)
    , m_scalingMode(1)
    , m_volume(1.0f)
    , m_lowLatencyMode(true)
    , m_connectionState(ConnectionState::Disconnected)
{
}

MainWindow::~MainWindow() {
    Shutdown();
}

bool MainWindow::Initialize() {
    LOG_INFO("Initializing main window UI...");
    LOG_INFO("Main window UI initialized successfully");
    return true;
}

void MainWindow::Shutdown() {
    LOG_INFO("Shutting down main window UI...");
    LOG_INFO("Main window UI shutdown complete");
}

void MainWindow::Render() {
    RenderMenuBar();
    
    if (m_showDeviceList) {
        RenderDeviceList();
    }
    
    if (m_showSettings) {
        RenderVideoSettings();
        RenderAudioSettings();
    }
    
    if (m_showStats) {
        RenderStatistics();
    }
    
    if (m_showAbout) {
        RenderAbout();
    }
    
    RenderConnectionStatus();
}

void MainWindow::RenderMenuBar() {
    if (ImGui::BeginMainMenuBar()) {
        if (ImGui::BeginMenu("File")) {
            if (ImGui::MenuItem("Exit")) {
                // Handle exit
            }
            ImGui::EndMenu();
        }
        
        if (ImGui::BeginMenu("View")) {
            ImGui::MenuItem("Device List", nullptr, &m_showDeviceList);
            ImGui::MenuItem("Settings", nullptr, &m_showSettings);
            ImGui::MenuItem("Statistics", nullptr, &m_showStats);
            ImGui::Separator();
            if (ImGui::MenuItem("Fullscreen", "F11", &m_fullscreen)) {
                if (OnToggleFullscreen) {
                    OnToggleFullscreen(m_fullscreen);
                }
            }
            ImGui::EndMenu();
        }
        
        if (ImGui::BeginMenu("Help")) {
            ImGui::MenuItem("About", nullptr, &m_showAbout);
            ImGui::EndMenu();
        }
        
        ImGui::EndMainMenuBar();
    }
}

void MainWindow::RenderDeviceList() {
    ImGui::Begin("iOS Devices", &m_showDeviceList);
    
    ImGui::Text("Available Devices:");
    ImGui::Separator();
    
    if (m_availableDevices.empty()) {
        ImGui::Text("No devices found. Make sure your iOS device is on the same network.");
        ImGui::Text("Enable AirPlay on your device and try again.");
    } else {
        for (size_t i = 0; i < m_availableDevices.size(); ++i) {
            const auto& device = m_availableDevices[i];
            
            ImGui::PushID((int)i);
            
            if (ImGui::Selectable(device.name.c_str(), m_selectedDevice == (int)i)) {
                m_selectedDevice = (int)i;
            }
            
            if (ImGui::IsItemHovered()) {
                ImGui::BeginTooltip();
                ImGui::Text("Address: %s:%d", device.address.c_str(), device.port);
                ImGui::Text("Status: %s", device.isConnected ? "Connected" : "Available");
                ImGui::EndTooltip();
            }
            
            ImGui::PopID();
        }
        
        ImGui::Separator();
        
        if (m_selectedDevice >= 0 && m_selectedDevice < (int)m_availableDevices.size()) {
            const auto& selectedDevice = m_availableDevices[m_selectedDevice];
            
            if (m_connectionState == ConnectionState::Disconnected) {
                if (ImGui::Button("Connect")) {
                    if (OnConnectToDevice) {
                        OnConnectToDevice(selectedDevice.deviceId);
                    }
                }
            } else if (m_connectionState == ConnectionState::Connected) {
                if (ImGui::Button("Disconnect")) {
                    if (OnDisconnectDevice) {
                        OnDisconnectDevice();
                    }
                }
            } else {
                ImGui::Text("Connecting...");
            }
        }
    }
    
    ImGui::End();
}

void MainWindow::RenderConnectionStatus() {
    ImGui::Begin("Connection Status");
    
    switch (m_connectionState) {
        case ConnectionState::Disconnected:
            ImGui::TextColored(ImVec4(0.8f, 0.8f, 0.8f, 1.0f), "Status: Disconnected");
            break;
        case ConnectionState::Discovering:
            ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "Status: Discovering devices...");
            break;
        case ConnectionState::Connecting:
            ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "Status: Connecting...");
            break;
        case ConnectionState::Connected:
            ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Status: Connected");
            if (!m_connectedDeviceName.empty()) {
                ImGui::Text("Device: %s", m_connectedDeviceName.c_str());
            }
            break;
        case ConnectionState::Error:
            ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "Status: Connection Error");
            break;
    }
    
    ImGui::End();
}

void MainWindow::RenderVideoSettings() {
    ImGui::Begin("Video Settings", &m_showSettings);
    
    ImGui::Text("Display Settings:");
    ImGui::Separator();
    
    const char* scalingModes[] = { "Stretch", "Fit", "Fill" };
    ImGui::Combo("Scaling Mode", &m_scalingMode, scalingModes, 3);
    
    ImGui::Checkbox("Fullscreen", &m_fullscreen);
    
    ImGui::Text("Quality Settings:");
    ImGui::Separator();
    
    ImGui::Text("Target Resolution: 4K (3840x2160)");
    ImGui::Text("Target Frame Rate: 60 FPS");
    ImGui::Text("Hardware Acceleration: Enabled");
    
    ImGui::End();
}

void MainWindow::RenderAudioSettings() {
    ImGui::Begin("Audio Settings", &m_showSettings);
    
    ImGui::Text("Playback Settings:");
    ImGui::Separator();
    
    ImGui::SliderFloat("Volume", &m_volume, 0.0f, 1.0f, "%.2f");
    ImGui::Checkbox("Low Latency Mode", &m_lowLatencyMode);
    
    ImGui::Text("Audio Format:");
    ImGui::Separator();
    
    ImGui::Text("Sample Rate: 48 kHz");
    ImGui::Text("Channels: Stereo");
    ImGui::Text("Bit Depth: 32-bit float");
    
    ImGui::End();
}

void MainWindow::RenderStatistics() {
    ImGui::Begin("Statistics", &m_showStats);
    
    if (m_connectionState == ConnectionState::Connected) {
        ImGui::Text("Connection Statistics:");
        ImGui::Separator();
        
        ImGui::Text("Video Frames Received: %llu", m_connectionStats.videoFramesReceived);
        ImGui::Text("Audio Frames Received: %llu", m_connectionStats.audioFramesReceived);
        ImGui::Text("Video Frames Dropped: %llu", m_connectionStats.videoFramesDropped);
        ImGui::Text("Audio Frames Dropped: %llu", m_connectionStats.audioFramesDropped);
        
        ImGui::Separator();
        
        ImGui::Text("Performance:");
        ImGui::Text("Current FPS: %.1f", m_connectionStats.currentFPS);
        ImGui::Text("Average Latency: %.2f ms", m_connectionStats.averageLatency);
        ImGui::Text("Bytes Received: %llu", m_connectionStats.bytesReceived);
        
        auto connectionTime = std::chrono::steady_clock::now() - m_connectionStats.connectionStart;
        auto seconds = std::chrono::duration_cast<std::chrono::seconds>(connectionTime).count();
        ImGui::Text("Connection Time: %lld seconds", seconds);
    } else {
        ImGui::Text("No connection active");
    }
    
    ImGui::End();
}

void MainWindow::RenderAbout() {
    ImGui::Begin("About iOS Mirror", &m_showAbout);
    
    ImGui::Text("iOS Mirror v1.0.0");
    ImGui::Text("4K@60fps Ultra Low Latency iOS Device Mirroring");
    ImGui::Separator();
    
    ImGui::Text("Features:");
    ImGui::BulletText("4K resolution support (3840x2160)");
    ImGui::BulletText("60 FPS target frame rate");
    ImGui::BulletText("Ultra-low latency streaming");
    ImGui::BulletText("Hardware-accelerated video decoding");
    ImGui::BulletText("High-quality audio processing");
    ImGui::BulletText("Automatic device discovery");
    
    ImGui::Separator();
    ImGui::Text("Built with DirectX 11, Media Foundation, and WASAPI");
    
    ImGui::End();
}
