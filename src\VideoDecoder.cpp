#include "VideoDecoder.h"
#include "Utils/Logger.h"
#include "Utils/Timer.h"

VideoDecoder::VideoDecoder(ID3D11Device* device, ID3D11DeviceContext* context)
    : m_d3dDevice(device)
    , m_d3dContext(context)
    , m_videoDevice(nullptr)
    , m_videoContext(nullptr)
    , m_videoProcessor(nullptr)
    , m_videoProcessorEnum(nullptr)
    , m_decoder(nullptr)
    , m_inputMediaType(nullptr)
    , m_outputMediaType(nullptr)
    , m_deviceManager(nullptr)
    , m_deviceManagerToken(0)
    , m_targetWidth(TARGET_WIDTH)
    , m_targetHeight(TARGET_HEIGHT)
    , m_targetFPS(TARGET_FPS)
    , m_hardwareAccelEnabled(true)
    , m_currentCodec(VideoCodec::Unknown)
    , m_isInitialized(false)
    , m_decoderReady(false)
    , m_decodedFrameCount(0)
    , m_totalDecodeTime(0.0)
    , m_averageDecodeTime(0.0)
    , m_shouldStop(false)
    , m_nv12ToRgbaShader(nullptr)
    , m_constantBuffer(nullptr)
    , m_inputSRV(nullptr)
    , m_outputUAV(nullptr)
{
    if (m_d3dDevice) {
        m_d3dDevice->AddRef();
    }
    if (m_d3dContext) {
        m_d3dContext->AddRef();
    }
}

VideoDecoder::~VideoDecoder() {
    Shutdown();
    
    SAFE_RELEASE(m_d3dDevice);
    SAFE_RELEASE(m_d3dContext);
}

bool VideoDecoder::Initialize() {
    LOG_INFO("Initializing video decoder...");

    if (!InitializeMediaFoundation()) {
        LOG_ERROR("Failed to initialize Media Foundation");
        return false;
    }

    if (m_hardwareAccelEnabled && !InitializeHardwareDecoding()) {
        LOG_WARNING("Failed to initialize hardware decoding, falling back to software");
        m_hardwareAccelEnabled = false;
    }

    // Start decode thread
    m_shouldStop = false;
    m_decodeThread = std::thread([this]() {
        while (!m_shouldStop) {
            std::unique_lock<std::mutex> lock(m_queueMutex);
            m_queueCondition.wait(lock, [this] { return !m_inputQueue.empty() || m_shouldStop; });

            if (m_shouldStop) break;

            if (!m_inputQueue.empty()) {
                VideoFrame frame = m_inputQueue.front();
                m_inputQueue.pop();
                lock.unlock();

                ID3D11Texture2D* outputTexture = nullptr;
                if (DecodeFrame(frame, &outputTexture)) {
                    if (OnFrameDecoded && outputTexture) {
                        OnFrameDecoded(outputTexture, frame.timestamp);
                    }
                }
            }
        }
    });

    m_isInitialized = true;
    LOG_INFO("Video decoder initialized successfully");
    return true;
}

void VideoDecoder::Shutdown() {
    LOG_INFO("Shutting down video decoder...");

    m_shouldStop = true;
    m_queueCondition.notify_all();

    if (m_decodeThread.joinable()) {
        m_decodeThread.join();
    }

    // Clear input queue
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        std::queue<VideoFrame> empty;
        m_inputQueue.swap(empty);
    }

    // Release texture pool
    {
        std::lock_guard<std::mutex> lock(m_poolMutex);
        for (auto texture : m_texturePool) {
            SAFE_RELEASE(texture);
        }
        m_texturePool.clear();
    }

    // Cleanup components
    ReleaseVideoDecoder();
    CleanupHardwareDecoding();
    CleanupMediaFoundation();

    // Release shaders
    SAFE_RELEASE(m_nv12ToRgbaShader);
    SAFE_RELEASE(m_constantBuffer);
    SAFE_RELEASE(m_inputSRV);
    SAFE_RELEASE(m_outputUAV);

    m_isInitialized = false;
    LOG_INFO("Video decoder shutdown complete");
}

void VideoDecoder::Update() {
    UpdatePerformanceStats();
}

bool VideoDecoder::DecodeFrame(const VideoFrame& inputFrame, ID3D11Texture2D** outputTexture) {
    if (!m_isInitialized || !outputTexture) {
        return false;
    }

    auto startTime = std::chrono::high_resolution_clock::now();

    // Detect codec if not already known
    if (m_currentCodec == VideoCodec::Unknown) {
        // Simple codec detection based on NAL unit headers
        if (inputFrame.data.size() >= 4) {
            uint8_t nalType = inputFrame.data[4] & 0x1F;
            if (nalType == 7 || nalType == 8) { // SPS/PPS for H.264
                m_currentCodec = VideoCodec::H264;
            } else if ((inputFrame.data[4] & 0x7E) >> 1 == 32) { // VPS for H.265
                m_currentCodec = VideoCodec::H265;
            }
        }

        if (m_currentCodec != VideoCodec::Unknown) {
            if (!CreateVideoDecoder(m_currentCodec, inputFrame.width, inputFrame.height)) {
                LOG_ERROR("Failed to create video decoder for codec");
                return false;
            }
        }
    }

    bool success = false;
    if (m_currentCodec == VideoCodec::H264) {
        success = ProcessH264Frame(inputFrame.data.data(), inputFrame.data.size(), outputTexture);
    } else if (m_currentCodec == VideoCodec::H265) {
        success = ProcessH265Frame(inputFrame.data.data(), inputFrame.data.size(), outputTexture);
    }

    if (success) {
        m_decodedFrameCount++;
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
        m_totalDecodeTime += duration.count() / 1000.0; // Convert to milliseconds
        m_averageDecodeTime = m_totalDecodeTime / m_decodedFrameCount;
    }

    return success;
}

bool VideoDecoder::ProcessH264Frame(const uint8_t* data, size_t size, ID3D11Texture2D** outputTexture) {
    if (!m_decoder || !data || size == 0) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_decodeMutex);

    HRESULT hr;
    
    // Create input sample
    IMFSample* inputSample = nullptr;
    hr = MFCreateSample(&inputSample);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to create input sample: 0x%08X", hr);
        return false;
    }

    // Create media buffer
    IMFMediaBuffer* mediaBuffer = nullptr;
    hr = MFCreateMemoryBuffer((DWORD)size, &mediaBuffer);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to create media buffer: 0x%08X", hr);
        inputSample->Release();
        return false;
    }

    // Copy data to buffer
    BYTE* bufferData = nullptr;
    hr = mediaBuffer->Lock(&bufferData, nullptr, nullptr);
    if (SUCCEEDED(hr)) {
        memcpy(bufferData, data, size);
        mediaBuffer->Unlock();
        mediaBuffer->SetCurrentLength((DWORD)size);
    }

    inputSample->AddBuffer(mediaBuffer);
    mediaBuffer->Release();

    if (FAILED(hr)) {
        inputSample->Release();
        return false;
    }

    // Process input
    hr = m_decoder->ProcessInput(0, inputSample, 0);
    inputSample->Release();

    if (FAILED(hr)) {
        LOG_ERROR("Failed to process input: 0x%08X", hr);
        return false;
    }

    // Get output
    MFT_OUTPUT_DATA_BUFFER outputBuffer = {};
    outputBuffer.dwStreamID = 0;
    
    DWORD status = 0;
    hr = m_decoder->ProcessOutput(0, 1, &outputBuffer, &status);
    
    if (hr == MF_E_TRANSFORM_NEED_MORE_INPUT) {
        return true; // Need more input data
    }
    
    if (FAILED(hr)) {
        LOG_ERROR("Failed to process output: 0x%08X", hr);
        return false;
    }

    if (outputBuffer.pSample) {
        bool result = ProcessMediaFoundationSample(outputBuffer.pSample, outputTexture);
        outputBuffer.pSample->Release();
        return result;
    }

    return false;
}

bool VideoDecoder::ProcessH265Frame(const uint8_t* data, size_t size, ID3D11Texture2D** outputTexture) {
    // Similar implementation to H264 but with H.265 specific handling
    return ProcessH264Frame(data, size, outputTexture); // Simplified for now
}

bool VideoDecoder::InitializeMediaFoundation() {
    HRESULT hr = MFStartup(MF_VERSION);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to startup Media Foundation: 0x%08X", hr);
        return false;
    }

    LOG_INFO("Media Foundation initialized");
    return true;
}

void VideoDecoder::CleanupMediaFoundation() {
    MFShutdown();
}

bool VideoDecoder::CreateVideoDecoder(VideoCodec codec, uint32_t width, uint32_t height) {
    ReleaseVideoDecoder();

    HRESULT hr;
    GUID decoderGuid;

    if (codec == VideoCodec::H264) {
        decoderGuid = CLSID_CMSH264DecoderMFT;
    } else if (codec == VideoCodec::H265) {
        decoderGuid = CLSID_CMSHEVCDecoderMFT;
    } else {
        LOG_ERROR("Unsupported codec");
        return false;
    }

    // Create decoder transform
    hr = CoCreateInstance(decoderGuid, nullptr, CLSCTX_INPROC_SERVER,
                         IID_IMFTransform, (void**)&m_decoder);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to create decoder transform: 0x%08X", hr);
        return false;
    }

    // Set hardware acceleration if available
    if (m_hardwareAccelEnabled && m_deviceManager) {
        hr = m_decoder->ProcessMessage(MFT_MESSAGE_SET_D3D_MANAGER,
                                      reinterpret_cast<ULONG_PTR>(m_deviceManager));
        if (FAILED(hr)) {
            LOG_WARNING("Failed to set D3D manager on decoder: 0x%08X", hr);
        }
    }

    // Create input media type
    hr = MFCreateMediaType(&m_inputMediaType);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to create input media type: 0x%08X", hr);
        return false;
    }

    m_inputMediaType->SetGUID(MF_MT_MAJOR_TYPE, MFMediaType_Video);
    if (codec == VideoCodec::H264) {
        m_inputMediaType->SetGUID(MF_MT_SUBTYPE, MFVideoFormat_H264);
    } else if (codec == VideoCodec::H265) {
        m_inputMediaType->SetGUID(MF_MT_SUBTYPE, MFVideoFormat_HEVC);
    }

    MFSetAttributeSize(m_inputMediaType, MF_MT_FRAME_SIZE, width, height);
    MFSetAttributeRatio(m_inputMediaType, MF_MT_FRAME_RATE, m_targetFPS, 1);

    hr = m_decoder->SetInputType(0, m_inputMediaType, 0);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to set input type: 0x%08X", hr);
        return false;
    }

    // Create output media type
    hr = MFCreateMediaType(&m_outputMediaType);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to create output media type: 0x%08X", hr);
        return false;
    }

    m_outputMediaType->SetGUID(MF_MT_MAJOR_TYPE, MFMediaType_Video);
    m_outputMediaType->SetGUID(MF_MT_SUBTYPE, MFVideoFormat_NV12);
    MFSetAttributeSize(m_outputMediaType, MF_MT_FRAME_SIZE, width, height);
    MFSetAttributeRatio(m_outputMediaType, MF_MT_FRAME_RATE, m_targetFPS, 1);

    hr = m_decoder->SetOutputType(0, m_outputMediaType, 0);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to set output type: 0x%08X", hr);
        return false;
    }

    // Start streaming
    hr = m_decoder->ProcessMessage(MFT_MESSAGE_NOTIFY_BEGIN_STREAMING, 0);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to begin streaming: 0x%08X", hr);
        return false;
    }

    hr = m_decoder->ProcessMessage(MFT_MESSAGE_NOTIFY_START_OF_STREAM, 0);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to start stream: 0x%08X", hr);
        return false;
    }

    m_decoderReady = true;
    LOG_INFO("Video decoder created successfully for %s (%dx%d)",
             codec == VideoCodec::H264 ? "H.264" : "H.265", width, height);
    return true;
}

void VideoDecoder::ReleaseVideoDecoder() {
    if (m_decoder) {
        m_decoder->ProcessMessage(MFT_MESSAGE_NOTIFY_END_OF_STREAM, 0);
        m_decoder->ProcessMessage(MFT_MESSAGE_NOTIFY_END_STREAMING, 0);
    }

    SAFE_RELEASE(m_outputMediaType);
    SAFE_RELEASE(m_inputMediaType);
    SAFE_RELEASE(m_decoder);

    m_decoderReady = false;
}

bool VideoDecoder::InitializeHardwareDecoding() {
    if (!m_d3dDevice) {
        return false;
    }

    HRESULT hr;

    // Create D3D11 video device
    hr = m_d3dDevice->QueryInterface(__uuidof(ID3D11VideoDevice), (void**)&m_videoDevice);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to create D3D11 video device: 0x%08X", hr);
        return false;
    }

    hr = m_d3dContext->QueryInterface(__uuidof(ID3D11VideoContext), (void**)&m_videoContext);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to create D3D11 video context: 0x%08X", hr);
        return false;
    }

    // Create device manager
    hr = MFCreateDXGIDeviceManager(&m_deviceManagerToken, &m_deviceManager);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to create DXGI device manager: 0x%08X", hr);
        return false;
    }

    hr = m_deviceManager->ResetDevice(m_d3dDevice, m_deviceManagerToken);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to reset device manager: 0x%08X", hr);
        return false;
    }

    LOG_INFO("Hardware decoding initialized");
    return true;
}

void VideoDecoder::CleanupHardwareDecoding() {
    SAFE_RELEASE(m_videoProcessorEnum);
    SAFE_RELEASE(m_videoProcessor);
    SAFE_RELEASE(m_videoContext);
    SAFE_RELEASE(m_videoDevice);
    SAFE_RELEASE(m_deviceManager);
}

void VideoDecoder::SetTargetResolution(uint32_t width, uint32_t height) {
    m_targetWidth = width;
    m_targetHeight = height;
}

void VideoDecoder::SetTargetFrameRate(uint32_t fps) {
    m_targetFPS = fps;
}

void VideoDecoder::EnableHardwareAcceleration(bool enable) {
    m_hardwareAccelEnabled = enable;
}

void VideoDecoder::UpdatePerformanceStats() {
    // Performance statistics are updated in DecodeFrame method
}
