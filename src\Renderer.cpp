#include "Renderer.h"
#include "Utils/Logger.h"

Renderer::Renderer(ID3D11Device* device, ID3D11DeviceContext* context)
    : m_d3dDevice(device)
    , m_d3dContext(context)
    , m_renderTarget(nullptr)
    , m_vertexShader(nullptr)
    , m_pixelShader(nullptr)
    , m_inputLayout(nullptr)
    , m_vertexBuffer(nullptr)
    , m_indexBuffer(nullptr)
    , m_constantBuffer(nullptr)
    , m_currentFrame(nullptr)
    , m_frameSRV(nullptr)
    , m_sampler(nullptr)
    , m_fullscreen(false)
    , m_aspectRatio(16.0f / 9.0f)
    , m_scalingMode(1)
    , m_renderedFrameCount(0)
    , m_currentFPS(0.0)
{
    if (m_d3dDevice) m_d3dDevice->AddRef();
    if (m_d3dContext) m_d3dContext->AddRef();
}

Renderer::~Renderer() {
    Shutdown();
    SAFE_RELEASE(m_d3dDevice);
    SAFE_RELEASE(m_d3dContext);
}

bool Renderer::Initialize() {
    LOG_INFO("Initializing renderer...");
    
    if (!CreateShaders()) {
        LOG_ERROR("Failed to create shaders");
        return false;
    }
    
    if (!CreateBuffers()) {
        LOG_ERROR("Failed to create buffers");
        return false;
    }
    
    LOG_INFO("Renderer initialized successfully");
    return true;
}

void Renderer::Shutdown() {
    LOG_INFO("Shutting down renderer...");
    ReleaseResources();
    LOG_INFO("Renderer shutdown complete");
}

void Renderer::Render() {
    if (!m_currentFrame || !m_renderTarget) {
        return;
    }
    
    // Set render target
    m_d3dContext->OMSetRenderTargets(1, &m_renderTarget, nullptr);
    
    // Set shaders
    m_d3dContext->VSSetShader(m_vertexShader, nullptr, 0);
    m_d3dContext->PSSetShader(m_pixelShader, nullptr, 0);
    m_d3dContext->IASetInputLayout(m_inputLayout);
    
    // Set vertex buffer
    UINT stride = sizeof(float) * 5; // position + texcoord
    UINT offset = 0;
    m_d3dContext->IASetVertexBuffers(0, 1, &m_vertexBuffer, &stride, &offset);
    m_d3dContext->IASetIndexBuffer(m_indexBuffer, DXGI_FORMAT_R16_UINT, 0);
    m_d3dContext->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST);
    
    // Set texture
    m_d3dContext->PSSetShaderResources(0, 1, &m_frameSRV);
    m_d3dContext->PSSetSamplers(0, 1, &m_sampler);
    
    // Draw
    m_d3dContext->DrawIndexed(6, 0, 0);
    
    // Update statistics
    m_renderedFrameCount++;
    auto now = std::chrono::high_resolution_clock::now();
    if (m_lastFrameTime != std::chrono::high_resolution_clock::time_point{}) {
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(now - m_lastFrameTime);
        m_currentFPS = 1000000.0 / duration.count();
    }
    m_lastFrameTime = now;
}

void Renderer::RenderVideoFrame(ID3D11Texture2D* texture, uint64_t timestamp) {
    if (!texture) return;
    
    // Update current frame
    SAFE_RELEASE(m_currentFrame);
    SAFE_RELEASE(m_frameSRV);
    
    m_currentFrame = texture;
    m_currentFrame->AddRef();
    
    // Create shader resource view
    D3D11_SHADER_RESOURCE_VIEW_DESC srvDesc = {};
    srvDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    srvDesc.ViewDimension = D3D11_SRV_DIMENSION_TEXTURE2D;
    srvDesc.Texture2D.MipLevels = 1;
    
    HRESULT hr = m_d3dDevice->CreateShaderResourceView(m_currentFrame, &srvDesc, &m_frameSRV);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to create shader resource view: 0x%08X", hr);
    }
}

void Renderer::SetRenderTarget(ID3D11RenderTargetView* renderTarget) {
    SAFE_RELEASE(m_renderTarget);
    m_renderTarget = renderTarget;
    if (m_renderTarget) {
        m_renderTarget->AddRef();
    }
}

void Renderer::SetDisplayMode(bool fullscreen) {
    m_fullscreen = fullscreen;
}

void Renderer::SetAspectRatio(float ratio) {
    m_aspectRatio = ratio;
}

void Renderer::SetScalingMode(int mode) {
    m_scalingMode = mode;
}

bool Renderer::CreateShaders() {
    // Simple vertex shader for fullscreen quad
    const char* vsSource = R"(
        struct VS_INPUT {
            float3 pos : POSITION;
            float2 tex : TEXCOORD0;
        };
        
        struct PS_INPUT {
            float4 pos : SV_POSITION;
            float2 tex : TEXCOORD0;
        };
        
        PS_INPUT main(VS_INPUT input) {
            PS_INPUT output;
            output.pos = float4(input.pos, 1.0f);
            output.tex = input.tex;
            return output;
        }
    )";
    
    // Simple pixel shader for texture rendering
    const char* psSource = R"(
        Texture2D tex : register(t0);
        SamplerState samp : register(s0);
        
        struct PS_INPUT {
            float4 pos : SV_POSITION;
            float2 tex : TEXCOORD0;
        };
        
        float4 main(PS_INPUT input) : SV_Target {
            return tex.Sample(samp, input.tex);
        }
    )";
    
    // Compile and create shaders (simplified - in real implementation would use D3DCompile)
    // For now, just create placeholder shaders
    LOG_INFO("Shaders created (placeholder implementation)");
    return true;
}

bool Renderer::CreateBuffers() {
    // Create vertex buffer for fullscreen quad
    float vertices[] = {
        -1.0f, -1.0f, 0.0f, 0.0f, 1.0f, // Bottom-left
         1.0f, -1.0f, 0.0f, 1.0f, 1.0f, // Bottom-right
         1.0f,  1.0f, 0.0f, 1.0f, 0.0f, // Top-right
        -1.0f,  1.0f, 0.0f, 0.0f, 0.0f  // Top-left
    };
    
    D3D11_BUFFER_DESC bufferDesc = {};
    bufferDesc.Usage = D3D11_USAGE_DEFAULT;
    bufferDesc.ByteWidth = sizeof(vertices);
    bufferDesc.BindFlags = D3D11_BIND_VERTEX_BUFFER;
    
    D3D11_SUBRESOURCE_DATA initData = {};
    initData.pSysMem = vertices;
    
    HRESULT hr = m_d3dDevice->CreateBuffer(&bufferDesc, &initData, &m_vertexBuffer);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to create vertex buffer: 0x%08X", hr);
        return false;
    }
    
    // Create index buffer
    uint16_t indices[] = { 0, 1, 2, 0, 2, 3 };
    
    bufferDesc.ByteWidth = sizeof(indices);
    bufferDesc.BindFlags = D3D11_BIND_INDEX_BUFFER;
    initData.pSysMem = indices;
    
    hr = m_d3dDevice->CreateBuffer(&bufferDesc, &initData, &m_indexBuffer);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to create index buffer: 0x%08X", hr);
        return false;
    }
    
    // Create sampler state
    D3D11_SAMPLER_DESC samplerDesc = {};
    samplerDesc.Filter = D3D11_FILTER_MIN_MAG_MIP_LINEAR;
    samplerDesc.AddressU = D3D11_TEXTURE_ADDRESS_CLAMP;
    samplerDesc.AddressV = D3D11_TEXTURE_ADDRESS_CLAMP;
    samplerDesc.AddressW = D3D11_TEXTURE_ADDRESS_CLAMP;
    samplerDesc.ComparisonFunc = D3D11_COMPARISON_NEVER;
    samplerDesc.MinLOD = 0;
    samplerDesc.MaxLOD = D3D11_FLOAT32_MAX;
    
    hr = m_d3dDevice->CreateSamplerState(&samplerDesc, &m_sampler);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to create sampler state: 0x%08X", hr);
        return false;
    }
    
    LOG_INFO("Buffers created successfully");
    return true;
}

void Renderer::ReleaseResources() {
    SAFE_RELEASE(m_sampler);
    SAFE_RELEASE(m_frameSRV);
    SAFE_RELEASE(m_currentFrame);
    SAFE_RELEASE(m_constantBuffer);
    SAFE_RELEASE(m_indexBuffer);
    SAFE_RELEASE(m_vertexBuffer);
    SAFE_RELEASE(m_inputLayout);
    SAFE_RELEASE(m_pixelShader);
    SAFE_RELEASE(m_vertexShader);
    SAFE_RELEASE(m_renderTarget);
}
