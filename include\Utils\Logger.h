#pragma once

#include "../Common.h"
#include <fstream>

enum class LogLevel {
    Debug = 0,
    Info = 1,
    Warning = 2,
    Error = 3
};

class Logger {
public:
    static void Initialize(const std::string& filename = "iosmirror.log");
    static void Shutdown();
    
    static void SetLevel(LogLevel level);
    static void SetConsoleOutput(bool enabled);
    
    static void Debug(const char* format, ...);
    static void Info(const char* format, ...);
    static void Warning(const char* format, ...);
    static void Error(const char* format, ...);
    
private:
    static void Log(LogLevel level, const char* format, va_list args);
    static std::string GetTimestamp();
    static std::string LevelToString(LogLevel level);
    
    static std::mutex s_mutex;
    static std::ofstream s_file;
    static LogLevel s_level;
    static bool s_consoleOutput;
    static bool s_initialized;
};
