#include "Application.h"
#include "Utils/Logger.h"

// Forward declaration for ImGui Win32 handler
extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // Enable console for debugging
#ifdef _DEBUG
    AllocConsole();
    freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
    freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);
    freopen_s((FILE**)stdin, "CONIN$", "r", stdin);
    std::wcout.clear();
    std::cout.clear();
    std::wcerr.clear();
    std::cerr.clear();
    std::wcin.clear();
    std::cin.clear();
#endif

    // Initialize logger
    Logger::Initialize("iosmirror.log");
    Logger::SetLevel(LogLevel::Debug);
    Logger::SetConsoleOutput(true);

    LOG_INFO("=== iOS Mirror Application Starting ===");
    LOG_INFO("Version: 1.0.0");
    LOG_INFO("Target: 4K@60fps Ultra Low Latency");

    int exitCode = 0;
    
    try {
        // Create and initialize application
        Application app;
        
        if (!app.Initialize()) {
            LOG_ERROR("Failed to initialize application");
            exitCode = -1;
        } else {
            LOG_INFO("Application initialized successfully, starting main loop");
            
            // Run main loop
            app.Run();
            
            LOG_INFO("Application main loop ended");
        }
        
        // Shutdown will be called automatically in destructor
        
    } catch (const std::exception& e) {
        LOG_ERROR("Unhandled exception: %s", e.what());
        exitCode = -2;
    } catch (...) {
        LOG_ERROR("Unknown unhandled exception");
        exitCode = -3;
    }

    LOG_INFO("=== iOS Mirror Application Exiting (code: %d) ===", exitCode);
    Logger::Shutdown();

#ifdef _DEBUG
    FreeConsole();
#endif

    return exitCode;
}
