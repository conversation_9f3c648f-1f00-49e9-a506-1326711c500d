#include "AudioProcessor.h"
#include "Utils/Logger.h"
#include "Utils/Timer.h"

AudioProcessor::AudioProcessor()
    : m_deviceEnumerator(nullptr)
    , m_audioDevice(nullptr)
    , m_audioClient(nullptr)
    , m_renderClient(nullptr)
    , m_audioClock(nullptr)
    , m_waveFormat(nullptr)
    , m_sampleRate(48000)
    , m_channelCount(2)
    , m_bitDepth(32)
    , m_bufferFrameCount(0)
    , m_currentCodec(AudioCodec::Unknown)
    , m_audioThread(nullptr)
    , m_audioEvent(nullptr)
    , m_shouldStop(false)
    , m_isPlaying(false)
    , m_volume(1.0f)
    , m_lowLatencyMode(true)
    , m_processedFrameCount(0)
    , m_totalLatency(0.0)
    , m_averageLatency(0.0)
    , m_bufferUnderruns(0)
    , m_lastVideoTimestamp(0)
    , m_playbackRateAdjustment(1.0)
    , m_aacDecoder(nullptr)
    , m_aacInputType(nullptr)
    , m_aacOutputType(nullptr)
    , m_aacDecoderInitialized(false)
{
}

AudioProcessor::~AudioProcessor() {
    Shutdown();
}

bool AudioProcessor::Initialize() {
    LOG_INFO("Initializing audio processor...");

    if (!InitializeWASAPI()) {
        LOG_ERROR("Failed to initialize WASAPI");
        return false;
    }

    // Initialize AAC decoder
    HRESULT hr = CoCreateInstance(CLSID_CAACAudioDecoder, nullptr, CLSCTX_INPROC_SERVER,
                                 IID_IMFTransform, (void**)&m_aacDecoder);
    if (SUCCEEDED(hr)) {
        m_aacDecoderInitialized = true;
        LOG_INFO("AAC decoder initialized");
    } else {
        LOG_WARNING("Failed to initialize AAC decoder: 0x%08X", hr);
    }

    LOG_INFO("Audio processor initialized successfully");
    return true;
}

void AudioProcessor::Shutdown() {
    LOG_INFO("Shutting down audio processor...");

    StopPlayback();
    CleanupWASAPI();

    // Cleanup AAC decoder
    SAFE_RELEASE(m_aacOutputType);
    SAFE_RELEASE(m_aacInputType);
    SAFE_RELEASE(m_aacDecoder);
    m_aacDecoderInitialized = false;

    // Clear audio queue
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        std::queue<AudioFrame> empty;
        m_audioQueue.swap(empty);
    }

    LOG_INFO("Audio processor shutdown complete");
}

void AudioProcessor::Update() {
    ProcessAudioQueue();
    
    // Update statistics
    if (m_processedFrameCount > 0) {
        m_averageLatency = m_totalLatency / m_processedFrameCount;
    }
}

bool AudioProcessor::ProcessAudioFrame(const AudioFrame& frame) {
    if (frame.data.empty()) {
        return false;
    }

    // Detect codec if not already known
    if (m_currentCodec == AudioCodec::Unknown) {
        // Simple codec detection - in real implementation, this would be more sophisticated
        if (frame.data.size() >= 2) {
            // Check for AAC ADTS header
            if ((frame.data[0] == 0xFF) && ((frame.data[1] & 0xF0) == 0xF0)) {
                m_currentCodec = AudioCodec::AAC;
            } else {
                m_currentCodec = AudioCodec::PCM; // Assume PCM if not AAC
            }
        }
    }

    // Add to processing queue
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        
        // Limit queue size to prevent memory buildup
        if (m_audioQueue.size() >= MAX_QUEUE_SIZE) {
            m_audioQueue.pop(); // Remove oldest frame
            m_bufferUnderruns++;
        }
        
        m_audioQueue.push(frame);
    }
    
    m_queueCondition.notify_one();
    return true;
}

bool AudioProcessor::DecodeAACFrame(const uint8_t* data, size_t size, std::vector<float>& output) {
    if (!m_aacDecoderInitialized || !data || size == 0) {
        return false;
    }

    HRESULT hr;
    
    // Create input sample
    IMFSample* inputSample = nullptr;
    hr = MFCreateSample(&inputSample);
    if (FAILED(hr)) {
        return false;
    }

    // Create media buffer
    IMFMediaBuffer* mediaBuffer = nullptr;
    hr = MFCreateMemoryBuffer((DWORD)size, &mediaBuffer);
    if (FAILED(hr)) {
        inputSample->Release();
        return false;
    }

    // Copy data to buffer
    BYTE* bufferData = nullptr;
    hr = mediaBuffer->Lock(&bufferData, nullptr, nullptr);
    if (SUCCEEDED(hr)) {
        memcpy(bufferData, data, size);
        mediaBuffer->Unlock();
        mediaBuffer->SetCurrentLength((DWORD)size);
    }

    inputSample->AddBuffer(mediaBuffer);
    mediaBuffer->Release();

    if (FAILED(hr)) {
        inputSample->Release();
        return false;
    }

    // Process input
    hr = m_aacDecoder->ProcessInput(0, inputSample, 0);
    inputSample->Release();

    if (FAILED(hr)) {
        return false;
    }

    // Get output
    MFT_OUTPUT_DATA_BUFFER outputBuffer = {};
    outputBuffer.dwStreamID = 0;
    
    DWORD status = 0;
    hr = m_aacDecoder->ProcessOutput(0, 1, &outputBuffer, &status);
    
    if (hr == MF_E_TRANSFORM_NEED_MORE_INPUT) {
        return true; // Need more input data
    }
    
    if (FAILED(hr) || !outputBuffer.pSample) {
        return false;
    }

    // Extract decoded audio data
    IMFMediaBuffer* outputMediaBuffer = nullptr;
    hr = outputBuffer.pSample->ConvertToContiguousBuffer(&outputMediaBuffer);
    if (SUCCEEDED(hr)) {
        BYTE* audioData = nullptr;
        DWORD audioDataLength = 0;
        
        hr = outputMediaBuffer->Lock(&audioData, nullptr, &audioDataLength);
        if (SUCCEEDED(hr)) {
            // Convert to float format
            size_t sampleCount = audioDataLength / sizeof(int16_t);
            output.resize(sampleCount);
            
            int16_t* samples = reinterpret_cast<int16_t*>(audioData);
            for (size_t i = 0; i < sampleCount; ++i) {
                output[i] = static_cast<float>(samples[i]) / 32768.0f;
            }
            
            outputMediaBuffer->Unlock();
        }
        
        outputMediaBuffer->Release();
    }

    outputBuffer.pSample->Release();
    return SUCCEEDED(hr);
}

bool AudioProcessor::DecodePCMFrame(const uint8_t* data, size_t size, std::vector<float>& output) {
    if (!data || size == 0) {
        return false;
    }

    // Assume 16-bit PCM for now
    size_t sampleCount = size / sizeof(int16_t);
    output.resize(sampleCount);
    
    const int16_t* samples = reinterpret_cast<const int16_t*>(data);
    for (size_t i = 0; i < sampleCount; ++i) {
        output[i] = static_cast<float>(samples[i]) / 32768.0f;
    }
    
    return true;
}

bool AudioProcessor::DecodeALACFrame(const uint8_t* data, size_t size, std::vector<float>& output) {
    // ALAC decoding would require additional library (like libavcodec)
    // For now, treat as PCM
    return DecodePCMFrame(data, size, output);
}

bool AudioProcessor::StartPlayback() {
    if (m_isPlaying) {
        return true;
    }

    if (!StartAudioClient()) {
        LOG_ERROR("Failed to start audio client");
        return false;
    }

    m_shouldStop = false;
    m_audioThread = CreateThread(nullptr, 0, AudioRenderThreadProc, this, 0, nullptr);
    if (!m_audioThread) {
        LOG_ERROR("Failed to create audio thread");
        StopAudioClient();
        return false;
    }

    m_isPlaying = true;
    LOG_INFO("Audio playback started");
    return true;
}

void AudioProcessor::StopPlayback() {
    if (!m_isPlaying) {
        return;
    }

    m_shouldStop = true;
    m_isPlaying = false;

    if (m_audioEvent) {
        SetEvent(m_audioEvent);
    }

    if (m_audioThread) {
        WaitForSingleObject(m_audioThread, INFINITE);
        CloseHandle(m_audioThread);
        m_audioThread = nullptr;
    }

    StopAudioClient();
    LOG_INFO("Audio playback stopped");
}

void AudioProcessor::PausePlayback() {
    if (m_audioClient) {
        m_audioClient->Stop();
    }
}

void AudioProcessor::ResumePlayback() {
    if (m_audioClient) {
        m_audioClient->Start();
    }
}

void AudioProcessor::SetVolume(float volume) {
    m_volume = std::clamp(volume, 0.0f, 1.0f);
}

void AudioProcessor::SetLatencyMode(bool lowLatency) {
    m_lowLatencyMode = lowLatency;
}

bool AudioProcessor::InitializeWASAPI() {
    HRESULT hr;

    // Initialize COM
    hr = CoInitializeEx(nullptr, COINIT_MULTITHREADED);
    if (FAILED(hr) && hr != RPC_E_CHANGED_MODE) {
        LOG_ERROR("Failed to initialize COM: 0x%08X", hr);
        return false;
    }

    // Create device enumerator
    hr = CoCreateInstance(__uuidof(MMDeviceEnumerator), nullptr, CLSCTX_ALL,
                         __uuidof(IMMDeviceEnumerator), (void**)&m_deviceEnumerator);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to create device enumerator: 0x%08X", hr);
        return false;
    }

    // Get default audio endpoint
    hr = m_deviceEnumerator->GetDefaultAudioEndpoint(eRender, eConsole, &m_audioDevice);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to get default audio endpoint: 0x%08X", hr);
        return false;
    }

    if (!CreateAudioClient()) {
        return false;
    }

    LOG_INFO("WASAPI initialized successfully");
    return true;
}

void AudioProcessor::CleanupWASAPI() {
    ReleaseAudioClient();

    SAFE_RELEASE(m_audioDevice);
    SAFE_RELEASE(m_deviceEnumerator);

    if (m_audioEvent) {
        CloseHandle(m_audioEvent);
        m_audioEvent = nullptr;
    }
}

bool AudioProcessor::CreateAudioClient() {
    if (!m_audioDevice) {
        return false;
    }

    HRESULT hr;

    // Activate audio client
    hr = m_audioDevice->Activate(__uuidof(IAudioClient), CLSCTX_ALL, nullptr, (void**)&m_audioClient);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to activate audio client: 0x%08X", hr);
        return false;
    }

    // Get mix format
    hr = m_audioClient->GetMixFormat(&m_waveFormat);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to get mix format: 0x%08X", hr);
        return false;
    }

    // Set up format for low latency
    m_waveFormat->wFormatTag = WAVE_FORMAT_IEEE_FLOAT;
    m_waveFormat->nChannels = m_channelCount;
    m_waveFormat->nSamplesPerSec = m_sampleRate;
    m_waveFormat->wBitsPerSample = 32;
    m_waveFormat->nBlockAlign = (m_waveFormat->nChannels * m_waveFormat->wBitsPerSample) / 8;
    m_waveFormat->nAvgBytesPerSec = m_waveFormat->nSamplesPerSec * m_waveFormat->nBlockAlign;
    m_waveFormat->cbSize = 0;

    // Initialize audio client
    DWORD streamFlags = AUDCLNT_STREAMFLAGS_EVENTCALLBACK;
    if (m_lowLatencyMode) {
        streamFlags |= AUDCLNT_STREAMFLAGS_LOOPBACK;
    }

    REFERENCE_TIME bufferDuration = m_lowLatencyMode ? 30000 : 100000; // 3ms or 10ms

    hr = m_audioClient->Initialize(AUDCLNT_SHAREMODE_SHARED, streamFlags,
                                  bufferDuration, 0, m_waveFormat, nullptr);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to initialize audio client: 0x%08X", hr);
        return false;
    }

    // Get buffer frame count
    hr = m_audioClient->GetBufferSize(&m_bufferFrameCount);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to get buffer size: 0x%08X", hr);
        return false;
    }

    // Create event for buffer notifications
    m_audioEvent = CreateEvent(nullptr, FALSE, FALSE, nullptr);
    if (!m_audioEvent) {
        LOG_ERROR("Failed to create audio event");
        return false;
    }

    hr = m_audioClient->SetEventHandle(m_audioEvent);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to set event handle: 0x%08X", hr);
        return false;
    }

    // Get render client
    hr = m_audioClient->GetService(__uuidof(IAudioRenderClient), (void**)&m_renderClient);
    if (FAILED(hr)) {
        LOG_ERROR("Failed to get render client: 0x%08X", hr);
        return false;
    }

    // Get audio clock
    hr = m_audioClient->GetService(__uuidof(IAudioClock), (void**)&m_audioClock);
    if (FAILED(hr)) {
        LOG_WARNING("Failed to get audio clock: 0x%08X", hr);
    }

    LOG_INFO("Audio client created - Sample rate: %d, Channels: %d, Buffer: %d frames",
             m_sampleRate, m_channelCount, m_bufferFrameCount);
    return true;
}

void AudioProcessor::ReleaseAudioClient() {
    SAFE_RELEASE(m_audioClock);
    SAFE_RELEASE(m_renderClient);
    SAFE_RELEASE(m_audioClient);

    if (m_waveFormat) {
        CoTaskMemFree(m_waveFormat);
        m_waveFormat = nullptr;
    }
}

bool AudioProcessor::StartAudioClient() {
    if (!m_audioClient) {
        return false;
    }

    HRESULT hr = m_audioClient->Start();
    if (FAILED(hr)) {
        LOG_ERROR("Failed to start audio client: 0x%08X", hr);
        return false;
    }

    return true;
}

void AudioProcessor::StopAudioClient() {
    if (m_audioClient) {
        m_audioClient->Stop();
    }
}

DWORD WINAPI AudioProcessor::AudioRenderThreadProc(LPVOID lpParameter) {
    AudioProcessor* processor = static_cast<AudioProcessor*>(lpParameter);
    processor->AudioRenderThread();
    return 0;
}

void AudioProcessor::AudioRenderThread() {
    LOG_INFO("Audio render thread started");

    while (!m_shouldStop) {
        DWORD waitResult = WaitForSingleObject(m_audioEvent, 100);

        if (waitResult != WAIT_OBJECT_0) {
            continue;
        }

        if (m_shouldStop) {
            break;
        }

        // Get current padding
        UINT32 numFramesPadding = 0;
        HRESULT hr = m_audioClient->GetCurrentPadding(&numFramesPadding);
        if (FAILED(hr)) {
            continue;
        }

        UINT32 numFramesAvailable = m_bufferFrameCount - numFramesPadding;
        if (numFramesAvailable == 0) {
            continue;
        }

        // Get buffer
        BYTE* data = nullptr;
        hr = m_renderClient->GetBuffer(numFramesAvailable, &data);
        if (FAILED(hr)) {
            continue;
        }

        // Fill buffer with audio data
        bool hasData = FillAudioBuffer(data, numFramesAvailable);

        // Release buffer
        DWORD flags = hasData ? 0 : AUDCLNT_BUFFERFLAGS_SILENT;
        m_renderClient->ReleaseBuffer(numFramesAvailable, flags);
    }

    LOG_INFO("Audio render thread ended");
}
