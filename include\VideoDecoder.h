#pragma once

#include "Common.h"

class VideoDecoder {
public:
    VideoDecoder(ID3D11Device* device, ID3D11DeviceContext* context);
    ~VideoDecoder();

    bool Initialize();
    void Shutdown();
    void Update();

    // Frame processing
    bool DecodeFrame(const VideoFrame& inputFrame, ID3D11Texture2D** outputTexture);
    bool ProcessH264Frame(const uint8_t* data, size_t size, ID3D11Texture2D** outputTexture);
    bool ProcessH265Frame(const uint8_t* data, size_t size, ID3D11Texture2D** outputTexture);

    // Configuration
    void SetTargetResolution(uint32_t width, uint32_t height);
    void SetTargetFrameRate(uint32_t fps);
    void EnableHardwareAcceleration(bool enable);

    // Statistics
    uint64_t GetDecodedFrameCount() const { return m_decodedFrameCount; }
    double GetAverageDecodeTime() const { return m_averageDecodeTime; }
    VideoCodec GetCurrentCodec() const { return m_currentCodec; }

    // Callbacks
    std::function<void(ID3D11Texture2D*, uint64_t)> OnFrameDecoded;

private:
    // Media Foundation
    bool InitializeMediaFoundation();
    void CleanupMediaFoundation();
    bool CreateVideoDecoder(VideoCodec codec, uint32_t width, uint32_t height);
    void ReleaseVideoDecoder();

    // Hardware acceleration
    bool InitializeHardwareDecoding();
    void CleanupHardwareDecoding();
    bool CreateD3D11VideoDevice();
    void ReleaseD3D11VideoDevice();

    // Frame processing
    bool ProcessMediaFoundationSample(IMFSample* sample, ID3D11Texture2D** outputTexture);
    bool ConvertNV12ToRGBA(ID3D11Texture2D* nv12Texture, ID3D11Texture2D** rgbaTexture);
    
    // Buffer management
    bool CreateOutputTexture(uint32_t width, uint32_t height, ID3D11Texture2D** texture);
    void ReleaseOutputTexture(ID3D11Texture2D* texture);
    
    // Performance monitoring
    void UpdatePerformanceStats();
    
    // DirectX
    ID3D11Device* m_d3dDevice;
    ID3D11DeviceContext* m_d3dContext;
    ID3D11VideoDevice* m_videoDevice;
    ID3D11VideoContext* m_videoContext;
    ID3D11VideoProcessor* m_videoProcessor;
    ID3D11VideoProcessorEnumerator* m_videoProcessorEnum;

    // Media Foundation
    IMFTransform* m_decoder;
    IMFMediaType* m_inputMediaType;
    IMFMediaType* m_outputMediaType;
    IMFDXGIDeviceManager* m_deviceManager;
    UINT m_deviceManagerToken;

    // Configuration
    uint32_t m_targetWidth;
    uint32_t m_targetHeight;
    uint32_t m_targetFPS;
    bool m_hardwareAccelEnabled;
    VideoCodec m_currentCodec;

    // State
    bool m_isInitialized;
    bool m_decoderReady;
    std::mutex m_decodeMutex;

    // Performance tracking
    uint64_t m_decodedFrameCount;
    double m_totalDecodeTime;
    double m_averageDecodeTime;
    std::chrono::high_resolution_clock::time_point m_lastDecodeTime;

    // Frame queue for async processing
    std::queue<VideoFrame> m_inputQueue;
    std::mutex m_queueMutex;
    std::condition_variable m_queueCondition;
    std::thread m_decodeThread;
    std::atomic<bool> m_shouldStop;

    // Output textures pool
    std::vector<ID3D11Texture2D*> m_texturePool;
    std::mutex m_poolMutex;
    static const size_t MAX_TEXTURE_POOL_SIZE = 8;

    // Shaders for format conversion
    ID3D11ComputeShader* m_nv12ToRgbaShader;
    ID3D11Buffer* m_constantBuffer;
    ID3D11ShaderResourceView* m_inputSRV;
    ID3D11UnorderedAccessView* m_outputUAV;
};
