cmake_minimum_required(VERSION 3.20)
project(iOSMirror VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Find required packages
find_package(PkgConfig REQUIRED)

# DirectX
find_path(DIRECTX_INCLUDE_DIR d3d11.h
    PATHS
    "$ENV{DXSDK_DIR}/Include"
    "$ENV{ProgramFiles}/Microsoft DirectX SDK*/Include"
    "C:/Program Files (x86)/Microsoft DirectX SDK (June 2010)/Include"
    "C:/Program Files/Microsoft DirectX SDK (June 2010)/Include"
)

find_library(D3D11_LIBRARY d3d11
    PATHS
    "$ENV{DXSDK_DIR}/Lib/x64"
    "$ENV{ProgramFiles}/Microsoft DirectX SDK*/Lib/x64"
    "C:/Program Files (x86)/Microsoft DirectX SDK (June 2010)/Lib/x64"
    "C:/Program Files/Microsoft DirectX SDK (June 2010)/Lib/x64"
)

find_library(DXGI_LIBRARY dxgi
    PATHS
    "$ENV{DXSDK_DIR}/Lib/x64"
    "$ENV{ProgramFiles}/Microsoft DirectX SDK*/Lib/x64"
    "C:/Program Files (x86)/Microsoft DirectX SDK (June 2010)/Lib/x64"
    "C:/Program Files/Microsoft DirectX SDK (June 2010)/Lib/x64"
)

# Windows libraries
set(WIN32_LIBRARIES
    kernel32
    user32
    gdi32
    winspool
    comdlg32
    advapi32
    shell32
    ole32
    oleaut32
    uuid
    odbc32
    odbccp32
    winmm
    ws2_32
    mfplat
    mf
    mfreadwrite
    mfuuid
    dnsapi
)

# Include directories
include_directories(
    ${CMAKE_SOURCE_DIR}/src
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/third_party/imgui
    ${CMAKE_SOURCE_DIR}/third_party/imgui/backends
    ${DIRECTX_INCLUDE_DIR}
)

# Source files
set(SOURCES
    src/main.cpp
    src/Application.cpp
    src/AirPlayReceiver.cpp
    src/VideoDecoder.cpp
    src/AudioProcessor.cpp
    src/Renderer.cpp
    src/DeviceDiscovery.cpp
    src/NetworkManager.cpp
    src/UI/MainWindow.cpp
    src/Utils/Logger.cpp
    src/Utils/Timer.cpp
    
    # ImGui sources
    third_party/imgui/imgui.cpp
    third_party/imgui/imgui_demo.cpp
    third_party/imgui/imgui_draw.cpp
    third_party/imgui/imgui_tables.cpp
    third_party/imgui/imgui_widgets.cpp
    third_party/imgui/backends/imgui_impl_win32.cpp
    third_party/imgui/backends/imgui_impl_dx11.cpp
)

# Header files
set(HEADERS
    include/Application.h
    include/AirPlayReceiver.h
    include/VideoDecoder.h
    include/AudioProcessor.h
    include/Renderer.h
    include/DeviceDiscovery.h
    include/NetworkManager.h
    include/UI/MainWindow.h
    include/Utils/Logger.h
    include/Utils/Timer.h
    include/Common.h
)

# Create executable
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(${PROJECT_NAME}
    ${D3D11_LIBRARY}
    ${DXGI_LIBRARY}
    ${WIN32_LIBRARIES}
)

# Compiler-specific options
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE /W4 /WX- /permissive-)
    target_compile_definitions(${PROJECT_NAME} PRIVATE _CRT_SECURE_NO_WARNINGS)
endif()

# Set as startup project in Visual Studio
set_property(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR} PROPERTY VS_STARTUP_PROJECT ${PROJECT_NAME})
