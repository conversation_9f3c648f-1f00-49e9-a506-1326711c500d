<!doctype html>
<html lang="en-us">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"/>
    <title>Dear ImGui Emscripten+GLFW+WebGPU example</title>
    <style>
        body { margin: 0; background-color: black }
        .emscripten {
            position: absolute;
            top: 0px;
            left: 0px;
            margin: 0px;
            border: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: block;
            image-rendering: optimizeSpeed;
            image-rendering: -moz-crisp-edges;
            image-rendering: -o-crisp-edges;
            image-rendering: -webkit-optimize-contrast;
            image-rendering: optimize-contrast;
            image-rendering: crisp-edges;
            image-rendering: pixelated;
            -ms-interpolation-mode: nearest-neighbor;
        }
    </style>
  </head>
  <body>
    <canvas class="emscripten" id="canvas" oncontextmenu="event.preventDefault()"></canvas>
    <script type='text/javascript'>
      var Module;
      (async () => {
        Module = {
          preRun: [],
          postRun: [],
          print: (function() {
              return function(text) {
                  text = Array.prototype.slice.call(arguments).join(' ');
                  console.log(text);
              };
          })(),
          printErr: function(text) {
              text = Array.prototype.slice.call(arguments).join(' ');
              console.error(text);
          },
          canvas: (function() {
              var canvas = document.getElementById('canvas');
              //canvas.addEventListener("webglcontextlost", function(e) { alert('FIXME: WebGL context lost, please reload the page'); e.preventDefault(); }, false);
              return canvas;
          })(),
          setStatus: function(text) {
              console.log("status: " + text);
          },
          monitorRunDependencies: function(left) {
              // no run dependencies to log
          }
        };
        window.onerror = function() {
          console.log("onerror: " + event);
        };

      // Initialize the graphics adapter
      {
          if (!navigator.gpu) {
            throw Error("WebGPU not supported.");
          }

          const adapter = await navigator.gpu.requestAdapter();
          const device = await adapter.requestDevice();
          Module.preinitializedWebGPUDevice = device;
      }

      {
          const js = document.createElement('script');
          js.async = true;
          js.src = "index.js";
          document.body.appendChild(js);
      }
      })();
    </script>
  </body>
</html>
