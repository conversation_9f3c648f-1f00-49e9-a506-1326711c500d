#pragma once

#include "Common.h"

class Application {
public:
    Application();
    ~Application();

    bool Initialize();
    void Run();
    void Shutdown();

    // Getters
    HWND GetWindowHandle() const { return m_hwnd; }
    ID3D11Device* GetD3DDevice() const { return m_d3dDevice; }
    ID3D11DeviceContext* GetD3DContext() const { return m_d3dContext; }
    IDXGISwapChain* GetSwapChain() const { return m_swapChain; }

    // Event handlers
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
    void OnResize(int width, int height);
    void OnClose();

    // Application state
    bool IsRunning() const { return m_isRunning; }
    void RequestExit() { m_isRunning = false; }

private:
    bool CreateWindow();
    bool InitializeD3D();
    bool InitializeImGui();
    void CleanupD3D();
    void CleanupImGui();
    
    void Update();
    void Render();
    void HandleMessages();

    // Window
    HWND m_hwnd;
    WNDCLASSEX m_wc;
    int m_windowWidth;
    int m_windowHeight;
    bool m_isRunning;

    // DirectX
    ID3D11Device* m_d3dDevice;
    ID3D11DeviceContext* m_d3dContext;
    IDXGISwapChain* m_swapChain;
    ID3D11RenderTargetView* m_renderTargetView;
    ID3D11DepthStencilView* m_depthStencilView;
    ID3D11Texture2D* m_depthStencilBuffer;

    // Components
    std::unique_ptr<AirPlayReceiver> m_airplayReceiver;
    std::unique_ptr<VideoDecoder> m_videoDecoder;
    std::unique_ptr<AudioProcessor> m_audioProcessor;
    std::unique_ptr<Renderer> m_renderer;
    std::unique_ptr<DeviceDiscovery> m_deviceDiscovery;
    std::unique_ptr<NetworkManager> m_networkManager;
    std::unique_ptr<MainWindow> m_mainWindow;

    // Threading
    std::thread m_updateThread;
    std::mutex m_stateMutex;
    std::atomic<bool> m_shouldExit;

    // Performance monitoring
    std::unique_ptr<Timer> m_frameTimer;
    double m_deltaTime;
    double m_fps;
    uint64_t m_frameCount;
};
