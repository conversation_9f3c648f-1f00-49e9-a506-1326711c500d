#pragma once

#include "Common.h"

class Renderer {
public:
    Render<PERSON>(ID3D11Device* device, ID3D11DeviceContext* context);
    ~Renderer();

    bool Initialize();
    void Shutdown();
    void Render();

    // Frame rendering
    void RenderVideoFrame(ID3D11Texture2D* texture, uint64_t timestamp);
    void SetRenderTarget(ID3D11RenderTargetView* renderTarget);
    
    // Display settings
    void SetDisplayMode(bool fullscreen);
    void SetAspectRatio(float ratio);
    void SetScalingMode(int mode); // 0=stretch, 1=fit, 2=fill
    
    // Statistics
    uint64_t GetRenderedFrameCount() const { return m_renderedFrameCount; }
    double GetCurrentFPS() const { return m_currentFPS; }

private:
    bool CreateShaders();
    bool CreateBuffers();
    void ReleaseResources();
    
    ID3D11Device* m_d3dDevice;
    ID3D11DeviceContext* m_d3dContext;
    ID3D11RenderTargetView* m_renderTarget;
    
    // Shaders
    ID3D11VertexShader* m_vertexShader;
    ID3D11PixelShader* m_pixelShader;
    ID3D11InputLayout* m_inputLayout;
    
    // Buffers
    ID3D11Buffer* m_vertexBuffer;
    ID3D11Buffer* m_indexBuffer;
    ID3D11Buffer* m_constantBuffer;
    
    // Textures
    ID3D11Texture2D* m_currentFrame;
    ID3D11ShaderResourceView* m_frameSRV;
    ID3D11SamplerState* m_sampler;
    
    // State
    bool m_fullscreen;
    float m_aspectRatio;
    int m_scalingMode;
    
    // Statistics
    uint64_t m_renderedFrameCount;
    double m_currentFPS;
    std::chrono::high_resolution_clock::time_point m_lastFrameTime;
};
