#include "Utils/Timer.h"

Timer::Timer() : m_isRunning(false) {
    Reset();
}

void Timer::Start() {
    m_startTime = std::chrono::high_resolution_clock::now();
    m_isRunning = true;
}

void Timer::Stop() {
    if (m_isRunning) {
        m_endTime = std::chrono::high_resolution_clock::now();
        m_isRunning = false;
    }
}

void Timer::Reset() {
    m_startTime = std::chrono::high_resolution_clock::now();
    m_endTime = m_startTime;
    m_isRunning = false;
}

double Timer::GetElapsedSeconds() const {
    auto endTime = m_isRunning ? std::chrono::high_resolution_clock::now() : m_endTime;
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(endTime - m_startTime);
    return duration.count() / 1000000000.0;
}

double Timer::GetElapsedMilliseconds() const {
    auto endTime = m_isRunning ? std::chrono::high_resolution_clock::now() : m_endTime;
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(endTime - m_startTime);
    return duration.count() / 1000000.0;
}

double Timer::GetElapsedMicroseconds() const {
    auto endTime = m_isRunning ? std::chrono::high_resolution_clock::now() : m_endTime;
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(endTime - m_startTime);
    return duration.count() / 1000.0;
}

uint64_t Timer::GetCurrentTimeUs() {
    return std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::high_resolution_clock::now().time_since_epoch()).count();
}

uint64_t Timer::GetCurrentTimeMs() {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now().time_since_epoch()).count();
}

void Timer::SleepUs(uint64_t microseconds) {
    std::this_thread::sleep_for(std::chrono::microseconds(microseconds));
}

void Timer::SleepMs(uint64_t milliseconds) {
    std::this_thread::sleep_for(std::chrono::milliseconds(milliseconds));
}
