#pragma once

#include "Common.h"

class AirPlayReceiver {
public:
    AirPlayReceiver();
    ~AirPlayReceiver();

    bool Initialize();
    void Shutdown();
    void Update();

    // Connection management
    bool StartServer();
    void StopServer();
    bool IsServerRunning() const { return m_isServerRunning; }

    // Client connection handling
    void HandleClientConnection(SOCKET clientSocket);
    void DisconnectClient();
    bool IsClientConnected() const { return m_isClientConnected; }

    // AirPlay protocol handlers
    void HandleRTSPRequest(const std::string& request, SOCKET clientSocket);
    void HandleSetupRequest(const std::string& request, SOCKET clientSocket);
    void HandlePlayRequest(const std::string& request, SOCKET clientSocket);
    void HandleTeardownRequest(const std::string& request, SOCKET clientSocket);

    // Video/Audio stream handlers
    void HandleVideoStream(const uint8_t* data, size_t size);
    void HandleAudioStream(const uint8_t* data, size_t size);

    // Callbacks
    std::function<void(const VideoFrame&)> OnVideoFrame;
    std::function<void(const AudioFrame&)> OnAudioFrame;
    std::function<void(const DeviceInfo&)> OnDeviceConnected;
    std::function<void()> OnDeviceDisconnected;

    // Statistics
    const ConnectionStats& GetStats() const { return m_stats; }

private:
    // Network handling
    bool CreateServerSocket();
    void CloseServerSocket();
    void AcceptConnections();
    void HandleClientData(SOCKET clientSocket);
    
    // RTSP protocol
    std::string ParseRTSPMethod(const std::string& request);
    std::map<std::string, std::string> ParseRTSPHeaders(const std::string& request);
    void SendRTSPResponse(SOCKET clientSocket, int statusCode, const std::string& statusText, 
                         const std::map<std::string, std::string>& headers = {}, 
                         const std::string& body = "");

    // Authentication
    bool HandleAuthentication(const std::string& request, SOCKET clientSocket);
    std::string GenerateAuthChallenge();
    bool ValidateAuthResponse(const std::string& response);

    // Stream processing
    void ProcessRTPPacket(const uint8_t* data, size_t size);
    void ProcessVideoRTP(const uint8_t* data, size_t size);
    void ProcessAudioRTP(const uint8_t* data, size_t size);

    // Encryption/Decryption
    bool InitializeCrypto();
    void CleanupCrypto();
    bool DecryptAESData(const uint8_t* encrypted, size_t encryptedSize, 
                       uint8_t* decrypted, size_t& decryptedSize);

    // Threading
    std::thread m_serverThread;
    std::thread m_clientThread;
    std::atomic<bool> m_shouldStop;
    std::mutex m_clientMutex;

    // Network
    SOCKET m_serverSocket;
    SOCKET m_clientSocket;
    sockaddr_in m_serverAddr;
    bool m_isServerRunning;
    bool m_isClientConnected;
    uint16_t m_serverPort;

    // Protocol state
    std::string m_sessionId;
    uint32_t m_cseq;
    bool m_isAuthenticated;
    std::string m_authChallenge;

    // Stream state
    uint16_t m_videoPort;
    uint16_t m_audioPort;
    uint32_t m_videoSSRC;
    uint32_t m_audioSSRC;
    
    // Buffers
    std::vector<uint8_t> m_videoBuffer;
    std::vector<uint8_t> m_audioBuffer;
    std::queue<VideoFrame> m_videoFrameQueue;
    std::queue<AudioFrame> m_audioFrameQueue;
    std::mutex m_videoQueueMutex;
    std::mutex m_audioQueueMutex;

    // Statistics
    ConnectionStats m_stats;
    std::chrono::steady_clock::time_point m_lastStatsUpdate;

    // Device info
    DeviceInfo m_connectedDevice;

    // Crypto
    uint8_t m_aesKey[16];
    uint8_t m_aesIV[16];
    bool m_cryptoInitialized;
};
