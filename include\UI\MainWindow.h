#pragma once

#include "../Common.h"

class MainWindow {
public:
    MainWindow();
    ~MainWindow();

    bool Initialize();
    void Shutdown();
    void Render();

    // UI state
    bool ShouldShowDeviceList() const { return m_showDeviceList; }
    bool ShouldShowSettings() const { return m_showSettings; }
    bool ShouldShowStats() const { return m_showStats; }

    // Callbacks
    std::function<void(const std::string&)> OnConnectToDevice;
    std::function<void()> OnDisconnectDevice;
    std::function<void(bool)> OnToggleFullscreen;

private:
    void RenderMenuBar();
    void RenderDeviceList();
    void RenderConnectionStatus();
    void RenderVideoSettings();
    void RenderAudioSettings();
    void RenderStatistics();
    void RenderAbout();

    // UI state
    bool m_showDeviceList;
    bool m_showSettings;
    bool m_showStats;
    bool m_showAbout;
    
    // Settings
    int m_selectedDevice;
    bool m_fullscreen;
    int m_scalingMode;
    float m_volume;
    bool m_lowLatencyMode;
    
    // Connection state
    ConnectionState m_connectionState;
    std::string m_connectedDeviceName;
    ConnectionStats m_connectionStats;
    
    // Device list
    std::vector<DeviceInfo> m_availableDevices;
};
