#include "DeviceDiscovery.h"
#include "Utils/Logger.h"

DeviceDiscovery::DeviceDiscovery()
    : m_isDiscovering(false)
    , m_shouldStop(false)
    , m_browseRef(nullptr)
{
}

DeviceDiscovery::~DeviceDiscovery() {
    Shutdown();
}

bool DeviceDiscovery::Initialize() {
    LOG_INFO("Initializing device discovery...");
    
    if (!InitializeBonjour()) {
        LOG_ERROR("Failed to initialize Bonjour");
        return false;
    }
    
    LOG_INFO("Device discovery initialized successfully");
    return true;
}

void DeviceDiscovery::Shutdown() {
    LOG_INFO("Shutting down device discovery...");
    
    StopDiscovery();
    CleanupBonjour();
    
    LOG_INFO("Device discovery shutdown complete");
}

void DeviceDiscovery::Update() {
    CheckDeviceTimeouts();
}

bool DeviceDiscovery::StartDiscovery() {
    if (m_isDiscovering) {
        return true;
    }
    
    m_shouldStop = false;
    m_discoveryThread = std::thread(&DeviceDiscovery::BrowseServices, this);
    m_isDiscovering = true;
    
    LOG_INFO("Device discovery started");
    return true;
}

void DeviceDiscovery::StopDiscovery() {
    if (!m_isDiscovering) {
        return;
    }
    
    m_shouldStop = true;
    m_isDiscovering = false;
    
    if (m_discoveryThread.joinable()) {
        m_discoveryThread.join();
    }
    
    LOG_INFO("Device discovery stopped");
}

std::vector<DeviceInfo> DeviceDiscovery::GetDiscoveredDevices() const {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    return m_devices;
}

DeviceInfo* DeviceDiscovery::FindDevice(const std::string& deviceId) {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    
    for (auto& device : m_devices) {
        if (device.deviceId == deviceId) {
            return &device;
        }
    }
    
    return nullptr;
}

void DeviceDiscovery::RemoveDevice(const std::string& deviceId) {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    
    m_devices.erase(
        std::remove_if(m_devices.begin(), m_devices.end(),
                      [&deviceId](const DeviceInfo& device) {
                          return device.deviceId == deviceId;
                      }),
        m_devices.end()
    );
}

bool DeviceDiscovery::InitializeBonjour() {
    // In a real implementation, this would initialize Bonjour/mDNS
    // For now, just return true as a placeholder
    LOG_INFO("Bonjour initialized (placeholder)");
    return true;
}

void DeviceDiscovery::CleanupBonjour() {
    // Cleanup Bonjour resources
    LOG_INFO("Bonjour cleanup (placeholder)");
}

void DeviceDiscovery::BrowseServices() {
    LOG_INFO("Service browsing thread started");
    
    while (!m_shouldStop) {
        // In a real implementation, this would browse for AirPlay services
        // For now, just simulate finding a device
        static bool deviceAdded = false;
        if (!deviceAdded) {
            DeviceInfo device;
            device.name = "Simulated iOS Device";
            device.address = "*************";
            device.port = 7000;
            device.deviceId = "sim-device-001";
            device.isConnected = false;
            device.lastSeen = std::chrono::steady_clock::now();
            
            AddOrUpdateDevice(device);
            deviceAdded = true;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
    
    LOG_INFO("Service browsing thread ended");
}

void DeviceDiscovery::AddOrUpdateDevice(const DeviceInfo& device) {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    
    // Check if device already exists
    for (auto& existingDevice : m_devices) {
        if (existingDevice.deviceId == device.deviceId) {
            existingDevice = device;
            existingDevice.lastSeen = std::chrono::steady_clock::now();
            return;
        }
    }
    
    // Add new device
    m_devices.push_back(device);
    
    if (OnDeviceFound) {
        OnDeviceFound(device);
    }
    
    LOG_INFO("Device found: %s (%s:%d)", device.name.c_str(), device.address.c_str(), device.port);
}

void DeviceDiscovery::CheckDeviceTimeouts() {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    
    auto now = std::chrono::steady_clock::now();
    auto timeout = std::chrono::seconds(30); // 30 second timeout
    
    for (auto it = m_devices.begin(); it != m_devices.end();) {
        if (now - it->lastSeen > timeout) {
            LOG_INFO("Device timeout: %s", it->name.c_str());
            
            if (OnDeviceLost) {
                OnDeviceLost(it->deviceId);
            }
            
            it = m_devices.erase(it);
        } else {
            ++it;
        }
    }
}

// NetworkManager implementation
NetworkManager::NetworkManager()
    : m_initialized(false)
{
}

NetworkManager::~NetworkManager() {
    Shutdown();
}

bool NetworkManager::Initialize() {
    LOG_INFO("Initializing network manager...");
    m_initialized = true;
    LOG_INFO("Network manager initialized successfully");
    return true;
}

void NetworkManager::Shutdown() {
    LOG_INFO("Shutting down network manager...");
    m_initialized = false;
    LOG_INFO("Network manager shutdown complete");
}

void NetworkManager::Update() {
    // Update network status
}

std::vector<std::string> NetworkManager::GetLocalIPAddresses() {
    std::vector<std::string> addresses;
    
    // Get local IP addresses (simplified implementation)
    char hostname[256];
    if (gethostname(hostname, sizeof(hostname)) == 0) {
        struct addrinfo hints = {};
        hints.ai_family = AF_INET;
        hints.ai_socktype = SOCK_STREAM;
        
        struct addrinfo* result = nullptr;
        if (getaddrinfo(hostname, nullptr, &hints, &result) == 0) {
            for (struct addrinfo* ptr = result; ptr != nullptr; ptr = ptr->ai_next) {
                struct sockaddr_in* sockaddr_ipv4 = (struct sockaddr_in*)ptr->ai_addr;
                char ip_str[INET_ADDRSTRLEN];
                inet_ntop(AF_INET, &sockaddr_ipv4->sin_addr, ip_str, INET_ADDRSTRLEN);
                addresses.push_back(ip_str);
            }
            freeaddrinfo(result);
        }
    }
    
    return addresses;
}

bool NetworkManager::IsNetworkAvailable() {
    return m_initialized;
}
