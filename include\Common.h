#pragma once

#include <windows.h>
#include <d3d11.h>
#include <dxgi.h>
#include <mfapi.h>
#include <mfidl.h>
#include <mfreadwrite.h>
#include <mmdeviceapi.h>
#include <audioclient.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <windns.h>

#include <iostream>
#include <memory>
#include <vector>
#include <string>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <atomic>
#include <chrono>
#include <functional>
#include <map>
#include <unordered_map>

// ImGui
#include "imgui.h"
#include "imgui_impl_win32.h"
#include "imgui_impl_dx11.h"

// Constants
constexpr int DEFAULT_AIRPLAY_PORT = 7000;
constexpr int DEFAULT_RAOP_PORT = 5000;
constexpr int MAX_VIDEO_BUFFER_SIZE = 1024 * 1024 * 10; // 10MB
constexpr int MAX_AUDIO_BUFFER_SIZE = 1024 * 64; // 64KB
constexpr int TARGET_FPS = 60;
constexpr int TARGET_WIDTH = 3840; // 4K width
constexpr int TARGET_HEIGHT = 2160; // 4K height

// Error handling macros
#define SAFE_RELEASE(p) { if(p) { (p)->Release(); (p) = nullptr; } }
#define CHECK_HR(hr) { if(FAILED(hr)) { Logger::Error("HRESULT failed: 0x%08X", hr); return false; } }
#define LOG_ERROR(msg, ...) Logger::Error(msg, ##__VA_ARGS__)
#define LOG_INFO(msg, ...) Logger::Info(msg, ##__VA_ARGS__)
#define LOG_DEBUG(msg, ...) Logger::Debug(msg, ##__VA_ARGS__)

// Forward declarations
class Application;
class AirPlayReceiver;
class VideoDecoder;
class AudioProcessor;
class Renderer;
class DeviceDiscovery;
class NetworkManager;
class MainWindow;
class Logger;
class Timer;

// Structures
struct VideoFrame {
    std::vector<uint8_t> data;
    uint32_t width;
    uint32_t height;
    uint64_t timestamp;
    bool isKeyFrame;
};

struct AudioFrame {
    std::vector<uint8_t> data;
    uint32_t sampleRate;
    uint32_t channels;
    uint64_t timestamp;
};

struct DeviceInfo {
    std::string name;
    std::string address;
    uint16_t port;
    std::string deviceId;
    bool isConnected;
    std::chrono::steady_clock::time_point lastSeen;
};

struct ConnectionStats {
    uint64_t videoFramesReceived;
    uint64_t audioFramesReceived;
    uint64_t videoFramesDropped;
    uint64_t audioFramesDropped;
    double averageLatency;
    double currentFPS;
    uint64_t bytesReceived;
    std::chrono::steady_clock::time_point connectionStart;
};

// Enums
enum class ConnectionState {
    Disconnected,
    Discovering,
    Connecting,
    Connected,
    Error
};

enum class VideoCodec {
    H264,
    H265,
    Unknown
};

enum class AudioCodec {
    AAC,
    PCM,
    ALAC,
    Unknown
};

// Utility functions
inline std::wstring StringToWString(const std::string& str) {
    if (str.empty()) return std::wstring();
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), NULL, 0);
    std::wstring wstrTo(size_needed, 0);
    MultiByteToWideChar(CP_UTF8, 0, &str[0], (int)str.size(), &wstrTo[0], size_needed);
    return wstrTo;
}

inline std::string WStringToString(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

inline uint64_t GetCurrentTimestamp() {
    return std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
}
