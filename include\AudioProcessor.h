#pragma once

#include "Common.h"

class AudioProcessor {
public:
    AudioProcessor();
    ~AudioProcessor();

    bool Initialize();
    void Shutdown();
    void Update();

    // Audio processing
    bool ProcessAudioFrame(const AudioFrame& frame);
    bool DecodeAACFrame(const uint8_t* data, size_t size, std::vector<float>& output);
    bool DecodePCMFrame(const uint8_t* data, size_t size, std::vector<float>& output);
    bool DecodeALACFrame(const uint8_t* data, size_t size, std::vector<float>& output);

    // Playback control
    bool StartPlayback();
    void StopPlayback();
    void PausePlayback();
    void ResumePlayback();
    bool IsPlaying() const { return m_isPlaying; }

    // Configuration
    void SetOutputDevice(const std::string& deviceId);
    void SetVolume(float volume); // 0.0 to 1.0
    void SetLatencyMode(bool lowLatency);
    
    // Audio properties
    uint32_t GetSampleRate() const { return m_sampleRate; }
    uint32_t GetChannelCount() const { return m_channelCount; }
    uint32_t GetBitDepth() const { return m_bitDepth; }
    AudioCodec GetCurrentCodec() const { return m_currentCodec; }

    // Statistics
    uint64_t GetProcessedFrameCount() const { return m_processedFrameCount; }
    double GetAverageLatency() const { return m_averageLatency; }
    uint32_t GetBufferUnderruns() const { return m_bufferUnderruns; }

    // Callbacks
    std::function<void(const float*, size_t)> OnAudioData;

private:
    // WASAPI
    bool InitializeWASAPI();
    void CleanupWASAPI();
    bool CreateAudioClient();
    void ReleaseAudioClient();
    bool StartAudioClient();
    void StopAudioClient();

    // Audio rendering thread
    void AudioRenderThread();
    static DWORD WINAPI AudioRenderThreadProc(LPVOID lpParameter);

    // Buffer management
    bool FillAudioBuffer(BYTE* buffer, UINT32 frameCount);
    void ProcessAudioQueue();
    
    // Format conversion
    bool ConvertToFloat32(const uint8_t* input, size_t inputSize, 
                         uint32_t inputSampleRate, uint32_t inputChannels,
                         std::vector<float>& output);
    bool ResampleAudio(const float* input, size_t inputFrames,
                      uint32_t inputSampleRate, uint32_t outputSampleRate,
                      uint32_t channels, std::vector<float>& output);

    // Synchronization
    void SynchronizeWithVideo(uint64_t videoTimestamp);
    void AdjustPlaybackRate(double adjustment);

    // WASAPI interfaces
    IMMDeviceEnumerator* m_deviceEnumerator;
    IMMDevice* m_audioDevice;
    IAudioClient* m_audioClient;
    IAudioRenderClient* m_renderClient;
    IAudioClock* m_audioClock;

    // Audio format
    WAVEFORMATEX* m_waveFormat;
    uint32_t m_sampleRate;
    uint32_t m_channelCount;
    uint32_t m_bitDepth;
    uint32_t m_bufferFrameCount;
    AudioCodec m_currentCodec;

    // Threading
    HANDLE m_audioThread;
    HANDLE m_audioEvent;
    std::atomic<bool> m_shouldStop;
    std::atomic<bool> m_isPlaying;

    // Audio queue
    std::queue<AudioFrame> m_audioQueue;
    std::mutex m_queueMutex;
    std::condition_variable m_queueCondition;
    static const size_t MAX_QUEUE_SIZE = 10;

    // Configuration
    std::string m_outputDeviceId;
    float m_volume;
    bool m_lowLatencyMode;

    // Statistics
    uint64_t m_processedFrameCount;
    double m_totalLatency;
    double m_averageLatency;
    uint32_t m_bufferUnderruns;
    std::chrono::high_resolution_clock::time_point m_lastProcessTime;

    // Synchronization with video
    uint64_t m_lastVideoTimestamp;
    double m_playbackRateAdjustment;
    std::mutex m_syncMutex;

    // Audio buffer for mixing/processing
    std::vector<float> m_mixBuffer;
    std::vector<float> m_tempBuffer;
    
    // Media Foundation for AAC decoding
    IMFTransform* m_aacDecoder;
    IMFMediaType* m_aacInputType;
    IMFMediaType* m_aacOutputType;
    bool m_aacDecoderInitialized;
};
