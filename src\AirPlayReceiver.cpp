#include "AirPlayReceiver.h"
#include "Utils/Logger.h"
#include <sstream>
#include <random>

AirPlayReceiver::AirPlayReceiver()
    : m_serverSocket(INVALID_SOCKET)
    , m_clientSocket(INVALID_SOCKET)
    , m_isServerRunning(false)
    , m_isClientConnected(false)
    , m_serverPort(DEFAULT_AIRPLAY_PORT)
    , m_cseq(0)
    , m_isAuthenticated(false)
    , m_videoPort(0)
    , m_audioPort(0)
    , m_videoSSRC(0)
    , m_audioSSRC(0)
    , m_cryptoInitialized(false)
    , m_shouldStop(false)
{
    ZeroMemory(&m_serverAddr, sizeof(m_serverAddr));
    ZeroMemory(m_aesKey, sizeof(m_aesKey));
    ZeroMemory(m_aesIV, sizeof(m_aesIV));
    
    // Initialize stats
    m_stats = {};
    m_lastStatsUpdate = std::chrono::steady_clock::now();
}

AirPlayReceiver::~AirPlayReceiver() {
    Shutdown();
}

bool AirPlayReceiver::Initialize() {
    LOG_INFO("Initializing AirPlay receiver...");

    if (!InitializeCrypto()) {
        LOG_ERROR("Failed to initialize crypto");
        return false;
    }

    if (!CreateServerSocket()) {
        LOG_ERROR("Failed to create server socket");
        return false;
    }

    LOG_INFO("AirPlay receiver initialized on port %d", m_serverPort);
    return true;
}

void AirPlayReceiver::Shutdown() {
    LOG_INFO("Shutting down AirPlay receiver...");
    
    StopServer();
    CloseServerSocket();
    CleanupCrypto();
    
    LOG_INFO("AirPlay receiver shutdown complete");
}

void AirPlayReceiver::Update() {
    // Update statistics
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - m_lastStatsUpdate);
    
    if (elapsed.count() >= 1) {
        // Calculate FPS
        if (m_stats.connectionStart != std::chrono::steady_clock::time_point{}) {
            auto totalTime = std::chrono::duration_cast<std::chrono::seconds>(now - m_stats.connectionStart);
            if (totalTime.count() > 0) {
                m_stats.currentFPS = static_cast<double>(m_stats.videoFramesReceived) / totalTime.count();
            }
        }
        
        m_lastStatsUpdate = now;
    }

    // Process queued frames
    ProcessVideoFrames();
    ProcessAudioFrames();
}

bool AirPlayReceiver::StartServer() {
    if (m_isServerRunning) {
        LOG_WARNING("Server is already running");
        return true;
    }

    m_shouldStop = false;
    m_serverThread = std::thread(&AirPlayReceiver::AcceptConnections, this);
    m_isServerRunning = true;
    
    LOG_INFO("AirPlay server started on port %d", m_serverPort);
    return true;
}

void AirPlayReceiver::StopServer() {
    if (!m_isServerRunning) {
        return;
    }

    m_shouldStop = true;
    m_isServerRunning = false;

    // Close client connection
    DisconnectClient();

    // Wait for server thread to finish
    if (m_serverThread.joinable()) {
        m_serverThread.join();
    }

    LOG_INFO("AirPlay server stopped");
}

void AirPlayReceiver::DisconnectClient() {
    std::lock_guard<std::mutex> lock(m_clientMutex);
    
    if (m_isClientConnected) {
        if (m_clientSocket != INVALID_SOCKET) {
            closesocket(m_clientSocket);
            m_clientSocket = INVALID_SOCKET;
        }
        
        if (m_clientThread.joinable()) {
            m_clientThread.join();
        }
        
        m_isClientConnected = false;
        m_isAuthenticated = false;
        
        // Reset session state
        m_sessionId.clear();
        m_cseq = 0;
        
        // Clear queues
        {
            std::lock_guard<std::mutex> videoLock(m_videoQueueMutex);
            std::queue<VideoFrame> empty;
            m_videoFrameQueue.swap(empty);
        }
        
        {
            std::lock_guard<std::mutex> audioLock(m_audioQueueMutex);
            std::queue<AudioFrame> empty;
            m_audioFrameQueue.swap(empty);
        }
        
        if (OnDeviceDisconnected) {
            OnDeviceDisconnected();
        }
        
        LOG_INFO("Client disconnected");
    }
}

bool AirPlayReceiver::CreateServerSocket() {
    m_serverSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (m_serverSocket == INVALID_SOCKET) {
        LOG_ERROR("Failed to create server socket: %d", WSAGetLastError());
        return false;
    }

    // Set socket options
    int opt = 1;
    if (setsockopt(m_serverSocket, SOL_SOCKET, SO_REUSEADDR, (char*)&opt, sizeof(opt)) == SOCKET_ERROR) {
        LOG_WARNING("Failed to set SO_REUSEADDR: %d", WSAGetLastError());
    }

    // Bind socket
    m_serverAddr.sin_family = AF_INET;
    m_serverAddr.sin_addr.s_addr = INADDR_ANY;
    m_serverAddr.sin_port = htons(m_serverPort);

    if (bind(m_serverSocket, (sockaddr*)&m_serverAddr, sizeof(m_serverAddr)) == SOCKET_ERROR) {
        LOG_ERROR("Failed to bind server socket: %d", WSAGetLastError());
        closesocket(m_serverSocket);
        m_serverSocket = INVALID_SOCKET;
        return false;
    }

    // Listen for connections
    if (listen(m_serverSocket, SOMAXCONN) == SOCKET_ERROR) {
        LOG_ERROR("Failed to listen on server socket: %d", WSAGetLastError());
        closesocket(m_serverSocket);
        m_serverSocket = INVALID_SOCKET;
        return false;
    }

    LOG_INFO("Server socket created and listening on port %d", m_serverPort);
    return true;
}

void AirPlayReceiver::CloseServerSocket() {
    if (m_serverSocket != INVALID_SOCKET) {
        closesocket(m_serverSocket);
        m_serverSocket = INVALID_SOCKET;
    }
}

void AirPlayReceiver::AcceptConnections() {
    LOG_INFO("Server thread started, waiting for connections...");
    
    while (!m_shouldStop) {
        sockaddr_in clientAddr;
        int clientAddrSize = sizeof(clientAddr);
        
        SOCKET clientSocket = accept(m_serverSocket, (sockaddr*)&clientAddr, &clientAddrSize);
        
        if (clientSocket == INVALID_SOCKET) {
            if (!m_shouldStop) {
                LOG_ERROR("Failed to accept client connection: %d", WSAGetLastError());
            }
            continue;
        }
        
        // Convert client address to string
        char clientIP[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &clientAddr.sin_addr, clientIP, INET_ADDRSTRLEN);
        
        LOG_INFO("Client connected from %s:%d", clientIP, ntohs(clientAddr.sin_port));
        
        // Handle client connection
        HandleClientConnection(clientSocket);
    }
    
    LOG_INFO("Server thread ended");
}

void AirPlayReceiver::HandleClientConnection(SOCKET clientSocket) {
    std::lock_guard<std::mutex> lock(m_clientMutex);

    // Disconnect existing client if any
    if (m_isClientConnected) {
        DisconnectClient();
    }

    m_clientSocket = clientSocket;
    m_isClientConnected = true;
    m_stats.connectionStart = std::chrono::steady_clock::now();

    // Start client handling thread
    m_clientThread = std::thread(&AirPlayReceiver::HandleClientData, this, clientSocket);

    // Populate device info
    sockaddr_in clientAddr;
    int addrSize = sizeof(clientAddr);
    if (getpeername(clientSocket, (sockaddr*)&clientAddr, &addrSize) == 0) {
        char clientIP[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &clientAddr.sin_addr, clientIP, INET_ADDRSTRLEN);

        m_connectedDevice.address = clientIP;
        m_connectedDevice.port = ntohs(clientAddr.sin_port);
        m_connectedDevice.isConnected = true;
        m_connectedDevice.lastSeen = std::chrono::steady_clock::now();
        m_connectedDevice.name = "iOS Device"; // Will be updated from device info

        if (OnDeviceConnected) {
            OnDeviceConnected(m_connectedDevice);
        }
    }
}

void AirPlayReceiver::HandleClientData(SOCKET clientSocket) {
    LOG_INFO("Client data handler thread started");

    char buffer[8192];
    std::string requestBuffer;

    while (m_isClientConnected && !m_shouldStop) {
        int bytesReceived = recv(clientSocket, buffer, sizeof(buffer) - 1, 0);

        if (bytesReceived <= 0) {
            if (bytesReceived == 0) {
                LOG_INFO("Client disconnected gracefully");
            } else {
                LOG_ERROR("Client connection error: %d", WSAGetLastError());
            }
            break;
        }

        buffer[bytesReceived] = '\0';
        requestBuffer += buffer;
        m_stats.bytesReceived += bytesReceived;

        // Process complete RTSP requests
        size_t pos = 0;
        while ((pos = requestBuffer.find("\r\n\r\n")) != std::string::npos) {
            std::string request = requestBuffer.substr(0, pos + 4);
            requestBuffer.erase(0, pos + 4);

            HandleRTSPRequest(request, clientSocket);
        }
    }

    LOG_INFO("Client data handler thread ended");
    DisconnectClient();
}

void AirPlayReceiver::HandleRTSPRequest(const std::string& request, SOCKET clientSocket) {
    LOG_DEBUG("Received RTSP request:\n%s", request.c_str());

    std::string method = ParseRTSPMethod(request);
    auto headers = ParseRTSPHeaders(request);

    // Update CSeq
    auto cseqIt = headers.find("CSeq");
    if (cseqIt != headers.end()) {
        m_cseq = std::stoul(cseqIt->second);
    }

    // Handle different RTSP methods
    if (method == "OPTIONS") {
        std::map<std::string, std::string> responseHeaders;
        responseHeaders["CSeq"] = std::to_string(m_cseq);
        responseHeaders["Public"] = "ANNOUNCE, SETUP, RECORD, PAUSE, FLUSH, TEARDOWN, OPTIONS, GET_PARAMETER, SET_PARAMETER";

        SendRTSPResponse(clientSocket, 200, "OK", responseHeaders);

    } else if (method == "ANNOUNCE") {
        // Handle stream announcement
        std::map<std::string, std::string> responseHeaders;
        responseHeaders["CSeq"] = std::to_string(m_cseq);

        SendRTSPResponse(clientSocket, 200, "OK", responseHeaders);

    } else if (method == "SETUP") {
        HandleSetupRequest(request, clientSocket);

    } else if (method == "RECORD") {
        HandlePlayRequest(request, clientSocket);

    } else if (method == "TEARDOWN") {
        HandleTeardownRequest(request, clientSocket);

    } else if (method == "GET_PARAMETER" || method == "SET_PARAMETER") {
        std::map<std::string, std::string> responseHeaders;
        responseHeaders["CSeq"] = std::to_string(m_cseq);

        SendRTSPResponse(clientSocket, 200, "OK", responseHeaders);

    } else {
        LOG_WARNING("Unhandled RTSP method: %s", method.c_str());

        std::map<std::string, std::string> responseHeaders;
        responseHeaders["CSeq"] = std::to_string(m_cseq);

        SendRTSPResponse(clientSocket, 501, "Not Implemented", responseHeaders);
    }
}

std::string AirPlayReceiver::ParseRTSPMethod(const std::string& request) {
    size_t spacePos = request.find(' ');
    if (spacePos != std::string::npos) {
        return request.substr(0, spacePos);
    }
    return "";
}

std::map<std::string, std::string> AirPlayReceiver::ParseRTSPHeaders(const std::string& request) {
    std::map<std::string, std::string> headers;
    std::istringstream iss(request);
    std::string line;

    // Skip the first line (method line)
    std::getline(iss, line);

    while (std::getline(iss, line) && !line.empty() && line != "\r") {
        size_t colonPos = line.find(':');
        if (colonPos != std::string::npos) {
            std::string key = line.substr(0, colonPos);
            std::string value = line.substr(colonPos + 1);

            // Trim whitespace
            key.erase(0, key.find_first_not_of(" \t\r\n"));
            key.erase(key.find_last_not_of(" \t\r\n") + 1);
            value.erase(0, value.find_first_not_of(" \t\r\n"));
            value.erase(value.find_last_not_of(" \t\r\n") + 1);

            headers[key] = value;
        }
    }

    return headers;
}

void AirPlayReceiver::SendRTSPResponse(SOCKET clientSocket, int statusCode, const std::string& statusText,
                                     const std::map<std::string, std::string>& headers,
                                     const std::string& body) {
    std::ostringstream response;
    response << "RTSP/1.0 " << statusCode << " " << statusText << "\r\n";

    for (const auto& header : headers) {
        response << header.first << ": " << header.second << "\r\n";
    }

    if (!body.empty()) {
        response << "Content-Length: " << body.length() << "\r\n";
    }

    response << "\r\n";

    if (!body.empty()) {
        response << body;
    }

    std::string responseStr = response.str();
    send(clientSocket, responseStr.c_str(), (int)responseStr.length(), 0);

    LOG_DEBUG("Sent RTSP response:\n%s", responseStr.c_str());
}

void AirPlayReceiver::HandleSetupRequest(const std::string& request, SOCKET clientSocket) {
    auto headers = ParseRTSPHeaders(request);

    // Parse transport header
    auto transportIt = headers.find("Transport");
    if (transportIt != headers.end()) {
        std::string transport = transportIt->second;

        // Extract port information
        size_t portPos = transport.find("control_port=");
        if (portPos != std::string::npos) {
            portPos += 13; // Length of "control_port="
            size_t endPos = transport.find(';', portPos);
            if (endPos == std::string::npos) endPos = transport.length();

            std::string portStr = transport.substr(portPos, endPos - portPos);
            m_videoPort = static_cast<uint16_t>(std::stoul(portStr));
        }

        portPos = transport.find("timing_port=");
        if (portPos != std::string::npos) {
            portPos += 12; // Length of "timing_port="
            size_t endPos = transport.find(';', portPos);
            if (endPos == std::string::npos) endPos = transport.length();

            std::string portStr = transport.substr(portPos, endPos - portPos);
            m_audioPort = static_cast<uint16_t>(std::stoul(portStr));
        }
    }

    // Generate session ID
    if (m_sessionId.empty()) {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(100000, 999999);
        m_sessionId = std::to_string(dis(gen));
    }

    std::map<std::string, std::string> responseHeaders;
    responseHeaders["CSeq"] = std::to_string(m_cseq);
    responseHeaders["Session"] = m_sessionId;
    responseHeaders["Transport"] = "RTP/AVP/UDP;unicast;mode=record;server_port=" +
                                  std::to_string(m_videoPort) + "-" + std::to_string(m_audioPort);

    SendRTSPResponse(clientSocket, 200, "OK", responseHeaders);

    LOG_INFO("Setup complete - Video port: %d, Audio port: %d, Session: %s",
             m_videoPort, m_audioPort, m_sessionId.c_str());
}

void AirPlayReceiver::HandlePlayRequest(const std::string& request, SOCKET clientSocket) {
    std::map<std::string, std::string> responseHeaders;
    responseHeaders["CSeq"] = std::to_string(m_cseq);
    responseHeaders["Session"] = m_sessionId;

    SendRTSPResponse(clientSocket, 200, "OK", responseHeaders);

    LOG_INFO("Playback started");
}

void AirPlayReceiver::HandleTeardownRequest(const std::string& request, SOCKET clientSocket) {
    std::map<std::string, std::string> responseHeaders;
    responseHeaders["CSeq"] = std::to_string(m_cseq);
    responseHeaders["Session"] = m_sessionId;

    SendRTSPResponse(clientSocket, 200, "OK", responseHeaders);

    LOG_INFO("Teardown requested");

    // Disconnect client after sending response
    std::thread([this]() {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        DisconnectClient();
    }).detach();
}

bool AirPlayReceiver::InitializeCrypto() {
    // Initialize AES key and IV (simplified - in real implementation,
    // these would be exchanged during handshake)
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<uint8_t> dis(0, 255);

    for (int i = 0; i < 16; ++i) {
        m_aesKey[i] = dis(gen);
        m_aesIV[i] = dis(gen);
    }

    m_cryptoInitialized = true;
    LOG_INFO("Crypto initialized");
    return true;
}

void AirPlayReceiver::CleanupCrypto() {
    ZeroMemory(m_aesKey, sizeof(m_aesKey));
    ZeroMemory(m_aesIV, sizeof(m_aesIV));
    m_cryptoInitialized = false;
}

void AirPlayReceiver::ProcessVideoFrames() {
    std::lock_guard<std::mutex> lock(m_videoQueueMutex);

    while (!m_videoFrameQueue.empty()) {
        VideoFrame frame = m_videoFrameQueue.front();
        m_videoFrameQueue.pop();

        if (OnVideoFrame) {
            OnVideoFrame(frame);
        }
    }
}

void AirPlayReceiver::ProcessAudioFrames() {
    std::lock_guard<std::mutex> lock(m_audioQueueMutex);

    while (!m_audioFrameQueue.empty()) {
        AudioFrame frame = m_audioFrameQueue.front();
        m_audioFrameQueue.pop();

        if (OnAudioFrame) {
            OnAudioFrame(frame);
        }
    }
}
